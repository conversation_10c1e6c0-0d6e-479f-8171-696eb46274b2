am4internal_webpackJsonp(["689e"],{XFs4:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};i.d(n,"GaugeChartDataItem",function(){return F}),i.d(n,"GaugeChart",function(){return Y}),i.d(n,"RadarChartDataItem",function(){return O}),i.d(n,"RadarChart",function(){return R}),i.d(n,"XYChartDataItem",function(){return a.b}),i.d(n,"XYChart",function(){return a.a}),i.d(n,"SerialChartDataItem",function(){return M.b}),i.d(n,"SerialChart",function(){return M.a}),i.d(n,"PieChart3DDataItem",function(){return E}),i.d(n,"PieChart3D",function(){return G}),i.d(n,"PieChartDataItem",function(){return W.b}),i.d(n,"PieChart",function(){return W.a}),i.d(n,"SlicedChart",function(){return K}),i.d(n,"SlicedChartDataItem",function(){return U}),i.d(n,"FlowDiagramDataItem",function(){return ye}),i.d(n,"FlowDiagram",function(){return ge}),i.d(n,"SankeyDiagramDataItem",function(){return Ae}),i.d(n,"SankeyDiagram",function(){return De}),i.d(n,"ChordDiagramDataItem",function(){return Le}),i.d(n,"ChordDiagram",function(){return ke}),i.d(n,"TreeMapDataItem",function(){return Fe}),i.d(n,"TreeMap",function(){return Ye}),i.d(n,"XYChart3DDataItem",function(){return qe}),i.d(n,"XYChart3D",function(){return Ue}),i.d(n,"ChartDataItem",function(){return Z.b}),i.d(n,"Chart",function(){return Z.a}),i.d(n,"LegendDataItem",function(){return re.b}),i.d(n,"Legend",function(){return re.a}),i.d(n,"LegendSettings",function(){return re.c}),i.d(n,"HeatLegend",function(){return Ke.a}),i.d(n,"SeriesDataItem",function(){return Ze.b}),i.d(n,"Series",function(){return Ze.a}),i.d(n,"XYSeriesDataItem",function(){return Qe.b}),i.d(n,"XYSeries",function(){return Qe.a}),i.d(n,"LineSeriesDataItem",function(){return s.b}),i.d(n,"LineSeries",function(){return s.a}),i.d(n,"LineSeriesSegment",function(){return Je.a}),i.d(n,"CandlestickSeriesDataItem",function(){return et}),i.d(n,"CandlestickSeries",function(){return tt}),i.d(n,"OHLCSeriesDataItem",function(){return nt}),i.d(n,"OHLCSeries",function(){return rt}),i.d(n,"ColumnSeriesDataItem",function(){return Re.b}),i.d(n,"ColumnSeries",function(){return Re.a}),i.d(n,"StepLineSeriesDataItem",function(){return ot}),i.d(n,"StepLineSeries",function(){return st}),i.d(n,"RadarSeriesDataItem",function(){return c}),i.d(n,"RadarSeries",function(){return p}),i.d(n,"RadarColumnSeriesDataItem",function(){return ut}),i.d(n,"RadarColumnSeries",function(){return ht}),i.d(n,"PieSeriesDataItem",function(){return X.b}),i.d(n,"PieSeries",function(){return X.a}),i.d(n,"FunnelSeries",function(){return ft}),i.d(n,"FunnelSeriesDataItem",function(){return gt}),i.d(n,"PyramidSeries",function(){return vt}),i.d(n,"PyramidSeriesDataItem",function(){return mt}),i.d(n,"PictorialStackedSeries",function(){return bt}),i.d(n,"PictorialStackedSeriesDataItem",function(){return xt}),i.d(n,"PieTick",function(){return Pt.a}),i.d(n,"FunnelSlice",function(){return pt}),i.d(n,"PieSeries3DDataItem",function(){return B}),i.d(n,"PieSeries3D",function(){return z}),i.d(n,"TreeMapSeriesDataItem",function(){return Se}),i.d(n,"TreeMapSeries",function(){return we}),i.d(n,"ColumnSeries3DDataItem",function(){return Ee}),i.d(n,"ColumnSeries3D",function(){return Ge}),i.d(n,"ConeSeriesDataItem",function(){return Dt}),i.d(n,"ConeSeries",function(){return It}),i.d(n,"CurvedColumnSeries",function(){return _t}),i.d(n,"CurvedColumnSeriesDataItem",function(){return Vt}),i.d(n,"AxisDataItem",function(){return Lt.b}),i.d(n,"Axis",function(){return Lt.a}),i.d(n,"Grid",function(){return x.a}),i.d(n,"AxisTick",function(){return kt.a}),i.d(n,"AxisLabel",function(){return Ot.a}),i.d(n,"AxisLine",function(){return Rt.a}),i.d(n,"AxisFill",function(){return f.a}),i.d(n,"AxisRenderer",function(){return g.a}),i.d(n,"AxisBreak",function(){return St.a}),i.d(n,"AxisBullet",function(){return A.a}),i.d(n,"ValueAxisDataItem",function(){return Oe.b}),i.d(n,"ValueAxis",function(){return Oe.a}),i.d(n,"CategoryAxisDataItem",function(){return T.b}),i.d(n,"CategoryAxis",function(){return T.a}),i.d(n,"CategoryAxisBreak",function(){return wt.a}),i.d(n,"DateAxisDataItem",function(){return Nt.b}),i.d(n,"DateAxis",function(){return Nt.a}),i.d(n,"DurationAxisDataItem",function(){return jt}),i.d(n,"DurationAxis",function(){return Ft}),i.d(n,"DateAxisBreak",function(){return Yt.a}),i.d(n,"ValueAxisBreak",function(){return Mt.a}),i.d(n,"AxisRendererX",function(){return Me.a}),i.d(n,"AxisRendererY",function(){return I.a}),i.d(n,"AxisRendererRadial",function(){return L}),i.d(n,"AxisLabelCircular",function(){return P.a}),i.d(n,"AxisRendererCircular",function(){return D}),i.d(n,"AxisFillCircular",function(){return v}),i.d(n,"GridCircular",function(){return b}),i.d(n,"AxisRendererX3D",function(){return We}),i.d(n,"AxisRendererY3D",function(){return Xe}),i.d(n,"Tick",function(){return dt.a}),i.d(n,"Bullet",function(){return se.a}),i.d(n,"LabelBullet",function(){return me}),i.d(n,"CircleBullet",function(){return Wt}),i.d(n,"ErrorBullet",function(){return Xt}),i.d(n,"XYChartScrollbar",function(){return Ht.a}),i.d(n,"ClockHand",function(){return j}),i.d(n,"FlowDiagramNode",function(){return ae}),i.d(n,"FlowDiagramLink",function(){return ce}),i.d(n,"SankeyNode",function(){return ve}),i.d(n,"SankeyLink",function(){return Pe}),i.d(n,"ChordNode",function(){return Te}),i.d(n,"ChordLink",function(){return _e}),i.d(n,"NavigationBarDataItem",function(){return qt}),i.d(n,"NavigationBar",function(){return Ut}),i.d(n,"Column",function(){return He.a}),i.d(n,"Candlestick",function(){return $e}),i.d(n,"OHLC",function(){return it}),i.d(n,"RadarColumn",function(){return lt}),i.d(n,"Column3D",function(){return ze}),i.d(n,"ConeColumn",function(){return At}),i.d(n,"CurvedColumn",function(){return Tt}),i.d(n,"XYCursor",function(){return Kt.a}),i.d(n,"Cursor",function(){return Zt.a}),i.d(n,"RadarCursor",function(){return Qt});var r=i("m4/l"),a=i("0Mwj"),o=i("tjMS"),s=i("v36H"),l=i("aCit"),u=i("Gg2j"),h=i("hGwe"),c=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeriesDataItem",t.setLocation("dateX",0,0),t.setLocation("dateY",0,0),t.setLocation("categoryX",0,0),t.setLocation("categoryY",0,0),t.applyTheme(),t}return r.c(t,e),t}(s.b),p=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeries",t.connectEnds=!0,t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.createDataItem=function(){return new c},t.prototype.getPoint=function(e,t,i,n,r,a,o){a||(a="valueX"),o||(o="valueY");var s=this.yAxis.getX(e,i,r,o),l=this.yAxis.getY(e,i,r,o),h=u.getDistance({x:s,y:l});0==h&&(h=1e-5);var c=this.xAxis.getAngle(e,t,n,a),p=this.chart.startAngle,d=this.chart.endAngle;return c<p||c>d?void 0:{x:h*u.cos(c),y:h*u.sin(c)}},t.prototype.addPoints=function(e,t,i,n,r){var a=this.getPoint(t,i,n,t.locations[i],t.locations[n]);a&&e.push(a)},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return h.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t.prototype.drawSegment=function(t,i,n){var r=this.yAxis.renderer;this.connectEnds&&360==Math.abs(r.endAngle-r.startAngle)&&(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)&&(i.push(i[0]),n.length>0&&n.unshift(n[n.length-1])),e.prototype.drawSegment.call(this,t,i,n)},Object.defineProperty(t.prototype,"connectEnds",{get:function(){return this.getPropertyValue("connectEnds")},set:function(e){this.setPropertyValue("connectEnds",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionBulletReal=function(e,t,i){var n=this.xAxis,r=this.yAxis;(t<n.start||t>n.end||i<r.start||i>r.end)&&(e.visible=!1),e.moveTo(this.xAxis.renderer.positionToPoint(t,i))},t.prototype.setXAxis=function(t){e.prototype.setXAxis.call(this,t),this.updateRendererRefs()},t.prototype.setYAxis=function(t){e.prototype.setYAxis.call(this,t),this.updateRendererRefs()},t.prototype.updateRendererRefs=function(){var e=this.xAxis.renderer,t=this.yAxis.renderer;e.axisRendererY=t},t}(s.a);l.b.registeredClasses.RadarSeries=p,l.b.registeredClasses.RadarSeriesDataItem=c;var d=i("C6dT"),y=i("FzPm"),g=i("Meme"),f=i("8EhG"),m=i("Mtpk"),v=function(e){function t(t){var i=e.call(this,t)||this;return i.className="AxisFillCircular",i.element=i.paper.add("path"),i.radius=Object(o.c)(100),i.applyTheme(),i}return r.c(t,e),t.prototype.draw=function(){if(e.prototype.draw.call(this),!this.__disabled&&!this.disabled&&this.axis){var t=this.axis.renderer;this.fillPath=t.getPositionRangePath(this.startPosition,this.endPosition,this.radius,m.hasValue(this.innerRadius)?this.innerRadius:t.innerRadius,this.cornerRadius),this.path=this.fillPath}},Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cornerRadius",{get:function(){return this.getPropertyValue("cornerRadius")},set:function(e){this.setPropertyValue("cornerRadius",e,!0)},enumerable:!0,configurable:!0}),t}(f.a);l.b.registeredClasses.AxisFillCircular=v;var x=i("AaJ4"),b=function(e){function t(){var t=e.call(this)||this;return t.className="GridCircular",t.pixelPerfect=!1,t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t}(x.a);l.b.registeredClasses.GridCircular=b;var P=i("IbTV"),C=i("v9UT"),A=i("5xph"),D=function(e){function t(){var t=e.call(this)||this;return t.pixelRadiusReal=0,t.layout="none",t.className="AxisRendererCircular",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.useChartAngles=!0,t.radius=Object(o.c)(100),t.isMeasured=!1,t.grid.template.location=0,t.labels.template.location=0,t.labels.template.radius=15,t.ticks.template.location=0,t.ticks.template.pixelPerfect=!1,t.tooltipLocation=0,t.line.strokeOpacity=0,t.applyTheme(),t}return r.c(t,e),t.prototype.setAxis=function(t){var i=this;e.prototype.setAxis.call(this,t),t.isMeasured=!1;var n=t.tooltip;n.adapter.add("dx",function(e,t){var n=C.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.cos(Math.atan2(n.y,n.x))-n.x}),n.adapter.add("dy",function(e,t){var n=C.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.sin(Math.atan2(n.y,n.x))-n.y})},t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return 2*Math.PI*this.pixelRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"useChartAngles",{get:function(){return this.getPropertyValue("useChartAngles")},set:function(e){this.setPropertyValue("useChartAngles",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return C.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e,t){m.isNumber(t)||(t=1);var i=this.positionToCoordinate(e),n=this.startAngle+(this.endAngle-this.startAngle)*i/this.axisLength,r=this.pixelRadius,a=this.pixelInnerRadius;if(this.axisRendererY){var o=u.fitToRange(this.axisRendererY.positionToCoordinate(t),0,1/0);return{x:o*u.cos(n),y:o*u.sin(n)}}return{x:u.cos(n)*a+(r-a)*u.cos(n)*t,y:u.sin(n)*a+(r-a)*u.sin(n)*t}},t.prototype.positionToAngle=function(e){var t,i=this.axis,n=(this.endAngle-this.startAngle)/(i.end-i.start);return t=i.renderer.inversed?this.startAngle+(i.end-e)*n:this.startAngle+(e-i.start)*n,u.round(t,3)},t.prototype.updateAxisLine=function(){var e=this.pixelRadius,t=this.startAngle,i=this.endAngle-t;this.line.path=h.moveTo({x:e*u.cos(t),y:e*u.sin(t)})+h.arcTo(t,i,e,e)},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(m.isNumber(n.x)&&m.isNumber(n.y)&&e.element){var r=u.DEGREES*Math.atan2(n.y,n.x),a=C.relativeRadiusToValue(m.hasValue(e.radius)?e.radius:Object(o.c)(100),this.pixelRadius),s=C.relativeRadiusToValue(e.innerRadius,this.pixelRadius);e.zIndex=0;var l=C.relativeRadiusToValue(m.isNumber(s)?s:this.innerRadius,this.pixelRadius,!0);e.path=h.moveTo({x:l*u.cos(r),y:l*u.sin(r)})+h.lineTo({x:a*u.cos(r),y:a*u.sin(r)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateTickElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var r=this.pixelRadius,a=u.DEGREES*Math.atan2(n.y,n.x),o=e.length;e.inside&&(o=-o),e.zIndex=1,e.path=h.moveTo({x:r*u.cos(a),y:r*u.sin(a)})+h.lineTo({x:(r+o)*u.cos(a),y:(r+o)*u.sin(a)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateBullet=function(e,t,i){var n=.5;e instanceof A.a&&(n=e.location),t+=(i-t)*n;var r=this.positionToPoint(t),a=this.pixelRadius,o=u.DEGREES*Math.atan2(r.y,r.x);r={x:a*u.cos(o),y:a*u.sin(o)},this.positionItem(e,r),this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,n){m.hasValue(n)||(n=e.location),t+=(i-t)*n,e.fixPosition(this.positionToAngle(t),this.pixelRadius),e.zIndex=2,this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t,i,n,r){var a="";if(m.isNumber(e)&&m.isNumber(t)){m.hasValue(i)||(i=this.radius),e=u.max(e,this.axis.start),(t=u.min(t,this.axis.end))<e&&(t=e);var o=C.relativeRadiusToValue(i,this.pixelRadius),s=C.relativeRadiusToValue(n,this.pixelRadius,!0),l=this.positionToAngle(e),c=this.positionToAngle(t)-l;a=h.arc(l,c,o,s,o,r)}return a},t.prototype.createGrid=function(){return new b},t.prototype.createFill=function(e){return new v(e)},t.prototype.createLabel=function(){return new P.a},t.prototype.pointToPosition=function(e){var t=u.fitAngleToRange(u.getAngle(e),this.startAngle,this.endAngle);return this.coordinateToPosition((t-this.startAngle)/360*this.axisLength)},t}(g.a);l.b.registeredClasses.AxisRendererCircular=D;var I=i("OXm9"),T=i("VB2N"),V=i("Vk33"),_=i("hD5A"),L=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.pixelRadiusReal=0,t.className="AxisRendererRadial",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.minGridDistance=30,t.gridType="circles",t.axisAngle=-90,t.isMeasured=!1,t.layout="none",t.radius=Object(o.c)(100),t.line.strokeOpacity=0,t.labels.template.horizontalCenter="middle",t._disposers.push(t._chart),t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return this.pixelRadius-this.pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return C.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){this._chart.set(e,null)},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e,t){var i=u.fitToRange(this.positionToCoordinate(e),0,1/0);return{x:i*u.cos(this.axisAngle),y:i*u.sin(this.axisAngle)}},t.prototype.updateAxisLine=function(){this.line.path=h.moveTo({x:this.pixelInnerRadius*u.cos(this.axisAngle),y:this.pixelInnerRadius*u.sin(this.axisAngle)})+h.lineTo({x:this.pixelRadius*u.cos(this.axisAngle),y:this.pixelRadius*u.sin(this.axisAngle)});var e=this.axis.title;e.valign="none",e.horizontalCenter="middle",e.verticalCenter="bottom",e.y=-this.axisLength/2;var t=90;this.opposite?this.inside||(t=-90):this.inside&&(t=-90),e.rotation=t},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n,r=this.positionToPoint(t),a=u.getDistance(r),o=this.startAngle,s=this.endAngle;if(m.isNumber(a)&&e.element){var l=this.chart,c=l.xAxes.getIndex(0),p=0,d=l.series.getIndex(0);if(d&&(p=d.dataItems.length),"polygons"==this.gridType&&p>0&&d&&c&&c instanceof T.a){var y=c.renderer.grid.template.location,g=c.getAngle(d.dataItems.getIndex(0),"categoryX",y);n=h.moveTo({x:a*u.cos(g),y:a*u.sin(g)});for(var f=1;f<p;f++)g=c.getAngle(d.dataItems.getIndex(f),"categoryX",y),n+=h.lineTo({x:a*u.cos(g),y:a*u.sin(g)});g=c.getAngle(d.dataItems.getIndex(p-1),"categoryX",c.renderer.cellEndLocation),n+=h.lineTo({x:a*u.cos(g),y:a*u.sin(g)})}else n=h.moveTo({x:a*u.cos(o),y:a*u.sin(o)})+h.arcTo(o,s-o,a,a);e.path=n}this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,n){m.hasValue(n)||(n=e.location),t+=(i-t)*n;var r=this.positionToPoint(t);this.positionItem(e,r),this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.updateBaseGridElement=function(){},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axisAngle",{get:function(){return this.getPropertyValue("axisAngle")},set:function(e){this.setPropertyValue("axisAngle",u.normalizeAngle(e)),this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"gridType",{get:function(){return this.chart.xAxes.getIndex(0)instanceof T.a?this.getPropertyValue("gridType"):"circles"},set:function(e){this.setPropertyValue("gridType",e,!0)},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t){var i,n=this.pixelInnerRadius,r=this.axisLength+n,a=u.fitToRange(this.positionToCoordinate(e),n,r),o=u.fitToRange(this.positionToCoordinate(t),n,r),s=this.startAngle,l=this.endAngle-s,c=this.chart,p=c.xAxes.getIndex(0),d=c.series.getIndex(0),y=0;if(d&&(y=d.dataItems.length),"polygons"==this.gridType&&y>0&&d&&p&&p instanceof T.a){var g=p.renderer.grid.template.location,f=p.getAngle(d.dataItems.getIndex(0),"categoryX",g);i=h.moveTo({x:o*u.cos(f),y:o*u.sin(f)});for(var m=1;m<y;m++)f=p.getAngle(d.dataItems.getIndex(m),"categoryX",g),i+=h.lineTo({x:o*u.cos(f),y:o*u.sin(f)});f=p.getAngle(d.dataItems.getIndex(y-1),"categoryX",p.renderer.cellEndLocation),i+=h.lineTo({x:o*u.cos(f),y:o*u.sin(f)}),i+=h.moveTo({x:a*u.cos(f),y:a*u.sin(f)});for(m=y-1;m>=0;m--)f=p.getAngle(d.dataItems.getIndex(m),"categoryX",g),i+=h.lineTo({x:a*u.cos(f),y:a*u.sin(f)})}else i=h.arc(s,l,o,a);return i},t.prototype.updateBreakElement=function(e){var t=e.startLine,i=e.endLine,n=e.fillShape,r=e.startPoint,a=e.endPoint;t.radius=Math.abs(r.y),i.radius=Math.abs(a.y),n.radius=Math.abs(a.y),n.innerRadius=Math.abs(r.y)},t.prototype.createBreakSprites=function(e){e.startLine=new V.a,e.endLine=new V.a,e.fillShape=new V.a},t.prototype.updateTooltip=function(){if(this.axis){var e=this.axisAngle;e<0&&(e+=360);var t="vertical";(e>45&&e<135||e>225&&e<315)&&(t="horizontal"),this.axis.updateTooltip(t,{x:-4e3,y:-4e3,width:8e3,height:8e3})}},t.prototype.updateTickElement=function(e,t){var i=this.positionToPoint(t);if(e.element){var n=u.normalizeAngle(this.axisAngle+90);n/90!=Math.round(n/90)?e.pixelPerfect=!1:e.pixelPerfect=!0;var r=-e.length;e.inside&&(r*=-1),e.path=h.moveTo({x:0,y:0})+h.lineTo({x:r*u.cos(n),y:r*u.sin(n)})}this.positionItem(e,i),this.toggleVisibility(e,t,0,1)},t.prototype.updateBullet=function(e,t,i){var n=.5;e instanceof A.a&&(n=e.location),t+=(i-t)*n;var r=this.positionToPoint(t);this.positionItem(e,r),this.toggleVisibility(e,t,0,1)},t.prototype.positionToCoordinate=function(e){var t,i=this.axis,n=i.axisFullLength,r=this.pixelInnerRadius;return t=i.renderer.inversed?(i.end-e)*n+r:(e-i.start)*n+r,u.round(t,1)},t.prototype.pointToPosition=function(e){var t=u.getDistance(e)-this.pixelInnerRadius;return this.coordinateToPosition(t)},t}(I.a);l.b.registeredClasses.AxisRendererRadial=L;var k=i("Wglt"),O=function(e){function t(){var t=e.call(this)||this;return t.className="RadarChartDataItem",t.applyTheme(),t}return r.c(t,e),t}(a.b),R=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=D,t._axisRendererY=L,t.innerRadiusModifyer=1,t.className="RadarChart",t.startAngle=-90,t.endAngle=270,t.radius=Object(o.c)(80),t.innerRadius=0;var i=t.plotContainer.createChild(d.a);return i.shouldClone=!1,i.layout="absolute",i.align="center",i.valign="middle",t.seriesContainer.parent=i,t.radarContainer=i,t.bulletsContainer.parent=i,t.axisBulletsContainer=i,t._cursorContainer=i,t._bulletMask=i.createChild(y.a),t._bulletMask.shouldClone=!1,t._bulletMask.element=t.paper.add("path"),t._bulletMask.opacity=0,t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Radar chart"))},t.prototype.processAxis=function(t){e.prototype.processAxis.call(this,t);var i=t.renderer;i.gridContainer.parent=i,i.breakContainer.parent=i,t.parent=this.radarContainer,i.toBack()},t.prototype.handleXAxisRangeChange=function(){e.prototype.handleXAxisRangeChange.call(this),k.each(this.yAxes.iterator(),function(e){e.invalidate()})},t.prototype.handleYAxisRangeChange=function(){e.prototype.handleYAxisRangeChange.call(this),k.each(this.xAxes.iterator(),function(e){e.invalidate()})},t.prototype.processConfig=function(t){if(t&&(m.hasValue(t.cursor)&&!m.hasValue(t.cursor.type)&&(t.cursor.type="RadarCursor"),m.hasValue(t.series)&&m.isArray(t.series)))for(var i=0,n=t.series.length;i<n;i++)t.series[i].type=t.series[i].type||"RadarSeries";e.prototype.processConfig.call(this,t)},t.prototype.beforeDraw=function(){e.prototype.beforeDraw.call(this);var t=this.plotContainer,i=u.getArcRect(this.startAngle,this.endAngle,1),n={x:0,y:0,width:0,height:0},r=t.innerWidth/i.width,a=t.innerHeight/i.height,s=this.innerRadius;if(s instanceof o.a){var l=s.value,c=Math.min(r,a);l=Math.max(c*l,c-Math.min(t.innerHeight,t.innerWidth))/c,n=u.getArcRect(this.startAngle,this.endAngle,l),this.innerRadiusModifyer=l/s.value,s=Object(o.c)(100*l)}i=u.getCommonRectangle([i,n]);var p=Math.min(t.innerWidth/i.width,t.innerHeight/i.height),d=2*C.relativeRadiusToValue(this.radius,p)||0,y=d/2,g=this.startAngle,f=this.endAngle;this._pixelInnerRadius=C.relativeRadiusToValue(s,y),this._bulletMask.path=h.arc(g,f-g,y,this._pixelInnerRadius),k.each(this.xAxes.iterator(),function(e){e.renderer.useChartAngles&&(e.renderer.startAngle=g,e.renderer.endAngle=f),e.width=d,e.height=d,e.renderer.pixelRadiusReal=y,e.renderer.innerRadius=s}),k.each(this.yAxes.iterator(),function(e){e.renderer.startAngle=g,e.renderer.endAngle=f,e.width=d,e.height=d,e.renderer.pixelRadiusReal=y,e.renderer.innerRadius=s});var m=this.cursor;m&&(m.width=d,m.height=d,m.startAngle=g,m.endAngle=f),this.radarContainer.definedBBox={x:y*i.x,y:y*i.y,width:y*i.width,height:y*i.height},this.radarContainer.validatePosition()},t.prototype.createSeries=function(){return new p},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return this._pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t.prototype.updateXAxis=function(e){e&&e.processRenderer()},t.prototype.updateYAxis=function(e){e&&e.processRenderer()},t}(a.a);l.b.registeredClasses.RadarChart=R;var S=i("vMqJ"),w=i("DziZ"),N=i("MIZb"),j=function(e){function t(){var t=e.call(this)||this;t._axis=new _.d,t.className="ClockHand";var i=new N.a;t.fill=i.getFor("alternativeBackground"),t.stroke=t.fill;var n=new y.a;n.radius=5,t.pin=n,t.isMeasured=!1,t.startWidth=5,t.endWidth=1,t.width=Object(o.c)(100),t.height=Object(o.c)(100),t.radius=Object(o.c)(100),t.innerRadius=Object(o.c)(0);var r=new w.a;return t.hand=r,t._disposers.push(t._axis),t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){e.prototype.validate.call(this);var t=this.hand;t.width=this.pixelWidth;var i=Math.max(this.startWidth,this.endWidth);if(t.height=i,t.leftSide=Object(o.c)(this.startWidth/i*100),t.rightSide=Object(o.c)(this.endWidth/i*100),this.axis){var n=this.axis.renderer,r=C.relativeRadiusToValue(this.innerRadius,n.pixelRadius),a=C.relativeRadiusToValue(this.radius,n.pixelRadius);t.x=r,t.y=-i/2,t.width=a-r}},Object.defineProperty(t.prototype,"pin",{get:function(){return this._pin},set:function(e){this._pin&&this.removeDispose(this._pin),e&&(this._pin=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hand",{get:function(){return this._hand},set:function(e){this._hand&&this.removeDispose(this._hand),e&&(this._hand=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startWidth",{get:function(){return this.getPropertyValue("startWidth")},set:function(e){this.setPropertyValue("startWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endWidth",{get:function(){return this.getPropertyValue("endWidth")},set:function(e){this.setPropertyValue("endWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"rotationDirection",{get:function(){return this.getPropertyValue("rotationDirection")},set:function(e){this.setPropertyValue("rotationDirection",e)},enumerable:!0,configurable:!0}),t.prototype.showValue=function(e,t,i){if(this._value=e,void 0!=e&&(m.isNumber(t)||(t=0),this.axis)){var n=this.axis.renderer.positionToAngle(this.axis.anyToPosition(e)),r=this.rotation;"clockWise"==this.rotationDirection&&n<r&&(this.rotation=r-360),"counterClockWise"==this.rotationDirection&&n>r&&(this.rotation=r+360),this.animate({property:"rotation",to:n},t,i)}},Object.defineProperty(t.prototype,"value",{get:function(){return this._value},set:function(e){this.showValue(e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axis",{get:function(){return this._axis.get()},set:function(e){if(this.axis!=e&&this._axis.set(e,new _.c([e.events.on("datavalidated",this.updateValue,this,!1),e.events.on("datarangechanged",this.updateValue,this,!1),e.events.on("dataitemsvalidated",this.updateValue,this,!1),e.events.on("propertychanged",this.invalidate,this,!1)])),e){var t=e.chart;t&&(this.rotation=t.startAngle)}this.parent=e.renderer,this.zIndex=5},enumerable:!0,configurable:!0}),t.prototype.updateValue=function(){this.value=this.value},t.prototype.processConfig=function(t){t&&m.hasValue(t.axis)&&m.isString(t.axis)&&this.map.hasKey(t.axis)&&(t.axis=this.map.getKey(t.axis)),e.prototype.processConfig.call(this,t)},t}(d.a);l.b.registeredClasses.ClockHand=j;var F=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChartDataItem",t.applyTheme(),t}return r.c(t,e),t}(O),Y=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChart",t.startAngle=180,t.endAngle=360,t.hands=new S.e(new j),t.hands.events.on("inserted",t.processHand,t,!1),t._disposers.push(new S.c(t.hands)),t._disposers.push(t.hands.template),t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Gauge chart"))},t.prototype.processHand=function(e){var t=e.newValue;t.axis||(t.axis=this.xAxes.getIndex(0))},t.prototype.configOrder=function(t,i){return t==i?0:"hands"==t?1:"hands"==i?-1:e.prototype.configOrder.call(this,t,i)},t}(R);l.b.registeredClasses.GaugeChart=Y;var M=i("2I/e"),W=i("quKg"),X=i("Puh1"),H=i("nPzZ"),B=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3DDataItem",t.values.depthValue={},t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"depthValue",{get:function(){return this.values.depthValue.value},set:function(e){this.setValue("depthValue",e)},enumerable:!0,configurable:!0}),t}(X.b),z=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3D",t.applyTheme(),t}return r.c(t,e),t.prototype.createDataItem=function(){return new B},t.prototype.createSlice=function(){return new H.a},t.prototype.validateDataElement=function(t){var i=t.slice,n=this.depth;m.isNumber(n)||(n=this.chart.depth);var r=t.values.depthValue.percent;m.isNumber(r)||(r=100),i.depth=r*n/100;var a=this.angle;m.isNumber(a)||(a=this.chart.angle),i.angle=a,e.prototype.validateDataElement.call(this,t)},t.prototype.validate=function(){e.prototype.validate.call(this);for(var t=this._workingStartIndex;t<this._workingEndIndex;t++){var i=this.dataItems.getIndex(t).slice,n=i.startAngle;n>=-90&&n<90?i.toFront():n>=90&&i.toBack()}},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e)},enumerable:!0,configurable:!0}),t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem.slice;t.y=t.pixelY-i.depth},t}(X.a);l.b.registeredClasses.PieSeries3D=z,l.b.registeredClasses.PieSeries3DDataItem=B;var E=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3DDataItem",t.applyTheme(),t}return r.c(t,e),t}(W.b),G=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3D",t.depth=20,t.angle=10,t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){e=u.fitToRange(e,0,90),this.setPropertyValue("angle",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new z},t}(W.a);l.b.registeredClasses.PieChart3D=G;var q=i("DXFp"),U=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChartDataItem",t.applyTheme(),t}return r.c(t,e),t}(q.b),K=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChart",t.seriesContainer.layout="horizontal",t.padding(15,15,15,15),t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sliced chart"))},t.prototype.validate=function(){e.prototype.validate.call(this)},t}(q.a);l.b.registeredClasses.SlicedChart=K,l.b.registeredClasses.SlicedChartDataItem=U;var Z=i("VIOb"),Q=i("+qIf"),J=i("Vs7R"),$=i("wUYf"),ee=i("MlsF"),te=i("3Cxr"),ie=i("CnhP"),ne=i("Qkdp"),re=i("uWmK"),ae=function(e){function t(){var t=e.call(this)||this;return t.legendSettings=new re.c,t.className="FlowDiagramNode",t.isMeasured=!1,new N.a,t.draggable=!0,t.inert=!0,t.setStateOnChildren=!0,t.events.on("positionchanged",t.invalidateLinks,t,!1),t.events.on("sizechanged",t.invalidateLinks,t,!1),t}return r.c(t,e),t.prototype.handleHit=function(e){this.isHidden||this.isHiding?this.show():this.hide()},t.prototype.show=function(t){var i=e.prototype.show.call(this,t);return this.outgoingDataItems.each(function(e){(!e.toNode||e.toNode&&!e.toNode.isHidden)&&e.setWorkingValue("value",e.getValue("value"),t)}),this.incomingDataItems.each(function(e){(!e.fromNode||e.fromNode&&!e.fromNode.isHidden)&&e.setWorkingValue("value",e.getValue("value"),t)}),i},t.prototype.hide=function(t){var i=e.prototype.hide.call(this,t);return this.outgoingDataItems.each(function(e){e.setWorkingValue("value",0,t)}),this.incomingDataItems.each(function(e){e.setWorkingValue("value",0,t)}),i},t.prototype.validate=function(){this.isDisposed()||(e.prototype.validate.call(this),this.invalidateLinks())},t.prototype.invalidateLinks=function(){var e=this;this.outgoingDataItems.each(function(t){var i=t.link;if("fromNode"==i.colorMode&&(i.fill=i.dataItem.fromNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var n=i.gradient.stops.getIndex(0);n&&(n.color=e.color,i.gradient.validate())}}),this.incomingDataItems.each(function(t){var i=t.link;if("toNode"==i.colorMode&&(i.fill=i.dataItem.toNode.color),"gradient"==i.colorMode){i.fill=i.gradient,i.stroke=i.gradient;var n=i.gradient.stops.getIndex(1);n&&(n.color=e.color,i.gradient.validate())}})},Object.defineProperty(t.prototype,"incomingDataItems",{get:function(){var e=this;if(!this._incomingDataItems){var t=new S.b;t.events.on("inserted",function(){"name"==e.chart.sortBy?e._incomingSorted=k.sort(e._incomingDataItems.iterator(),function(e,t){return $.order(e.fromName,t.fromName)}):"value"==e.chart.sortBy?e._incomingSorted=k.sort(e._incomingDataItems.iterator(),function(e,t){return ee.b(te.order(e.value,t.value))}):e._incomingSorted=e._incomingDataItems.iterator()},void 0,!1),this._incomingDataItems=t}return this._incomingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"outgoingDataItems",{get:function(){var e=this;if(!this._outgoingDataItems){var t=new S.b;t.events.on("inserted",function(){"name"==e.chart.sortBy?e._outgoingSorted=k.sort(e._outgoingDataItems.iterator(),function(e,t){return $.order(e.fromName,t.fromName)}):"value"==e.chart.sortBy?e._outgoingSorted=k.sort(e._outgoingDataItems.iterator(),function(e,t){return ee.b(te.order(e.value,t.value))}):e._outgoingSorted=e._outgoingDataItems.iterator()},void 0,!1),this._outgoingDataItems=t}return this._outgoingDataItems},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this.getPropertyValue("name")},set:function(e){this.setPropertyValue("name",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"total",{get:function(){return this.getPropertyValue("total")},set:function(e){this.setPropertyValue("total",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"totalIncoming",{get:function(){return this.getPropertyValue("totalIncoming")},set:function(e){this.setPropertyValue("totalIncoming",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"totalOutgoing",{get:function(){return this.getPropertyValue("totalOutgoing")},set:function(e){this.setPropertyValue("totalOutgoing",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.getPropertyValue("color")},set:function(e){this.setColorProperty("color",e),this._background&&(this._background.fill=e),this.fill=e},enumerable:!0,configurable:!0}),t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n=e.createChild(ie.a);n.shouldClone=!1,ne.copyProperties(this,n,J.b),n.stroke=this.fill,n.copyFrom(this),n.padding(0,0,0,0),n.width=t,n.height=i;var r=e.dataItem;r.color=n.fill,r.colorOrig=n.fill},Object.defineProperty(t.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(e){this._legendDataItem=e,this._legendDataItem.itemContainer.deepInvalidate()},enumerable:!0,configurable:!0}),t}(d.a);l.b.registeredClasses.FlowDiagramNode=ae;var oe=i("sxA1"),se=i("TXRX"),le=i("8ZqG"),ue=i("jfaP"),he=i("PTiM"),ce=function(e){function t(){var t=e.call(this)||this;t.className="FlowDiagramLink";var i=new N.a;return t.maskBullets=!1,t.colorMode="fromNode",t.layout="none",t.isMeasured=!1,t.startAngle=0,t.endAngle=0,t.strokeOpacity=0,t.verticalCenter="none",t.horizontalCenter="none",t.tooltipText="{fromName}→{toName}:{value.value}",t.tooltipLocation=.5,t.link=t.createChild(J.a),t.link.shouldClone=!1,t.link.setElement(t.paper.add("path")),t.link.isMeasured=!1,t.fillOpacity=.2,t.fill=i.getFor("alternativeBackground"),t.applyTheme(),t}return r.c(t,e),t.prototype.positionBullets=function(){var e=this;k.each(this.bullets.iterator(),function(t){t.parent=e.bulletsContainer,t.maxWidth=e.maxWidth,t.maxHeight=e.maxHeight,e.positionBullet(t)})},Object.defineProperty(t.prototype,"bulletsContainer",{get:function(){if(!this._bulletsContainer){var e=this.createChild(d.a);e.shouldClone=!1,e.layout="none",this._bulletsContainer=e}return this._bulletsContainer},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bulletsMask",{get:function(){if(!this._bulletsMask){var e=this.createChild(J.a);e.shouldClone=!1,e.setElement(this.paper.add("path")),e.isMeasured=!1,this._bulletsMask=e}return this._bulletsMask},enumerable:!0,configurable:!0}),t.prototype.positionBullet=function(e){var t=e.locationX;m.isNumber(t)||(t=e.locationY),m.isNumber(t)||(t=.5);var i=this.middleLine.positionToPoint(t);e.moveTo(i);var n,r=e.propertyFields.rotation;e.dataItem&&(n=e.dataItem.dataContext[r]);m.isNumber(n)||(n=i.angle),e.rotation=n},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"colorMode",{get:function(){return this.getPropertyValue("colorMode")},set:function(e){if("gradient"==e){var t=this.fill;this.gradient.stops.clear(),t instanceof le.a&&(this.gradient.addColor(t),this.gradient.addColor(t)),this.fill=this.gradient,this.stroke=this.gradient}this.setPropertyValue("colorMode",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"maskBullets",{get:function(){return this.getPropertyValue("maskBullets")},set:function(e){this.setPropertyValue("maskBullets",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tooltipLocation",{get:function(){return this.getPropertyValue("tooltipLocation")},set:function(e){this.setPropertyValue("tooltipLocation",e,!0)},enumerable:!0,configurable:!0}),t.prototype.setFill=function(t){e.prototype.setFill.call(this,t);var i=this._gradient;i&&t instanceof le.a&&(i.stops.clear(),i.addColor(t),i.addColor(t))},t.prototype.measureElement=function(){},Object.defineProperty(t.prototype,"bullets",{get:function(){var e=this;return this._bullets||(this._bullets=new S.e(new se.a),this._disposers.push(new S.c(this._bullets)),this._disposers.push(this._bullets.template),this._bullets.events.on("inserted",function(t){t.newValue.events.on("propertychanged",function(t){"locationX"!=t.property&&"locationY"!=t.property||e.positionBullet(t.target)},void 0,!1)},void 0,!1)),this._bullets},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.bullets.copyFrom(t.bullets);var i=this.middleLine;i&&(i instanceof he.a&&t.middleLine instanceof he.a&&i.copyFrom(t.middleLine),i instanceof ue.a&&t.middleLine instanceof ue.a&&i.copyFrom(t.middleLine)),this.link.copyFrom(t.link)},t.prototype.getTooltipX=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).x},t.prototype.getTooltipY=function(){if(this.middleLine)return this.middleLine.positionToPoint(this.tooltipLocation).y},Object.defineProperty(t.prototype,"gradient",{get:function(){return this._gradient||(this._gradient=new oe.a),this._gradient},enumerable:!0,configurable:!0}),t}(d.a);l.b.registeredClasses.FlowDiagramLink=ce;var pe=i("../../e9j.html"),de=i("DHte"),ye=function(e){function t(){var t=e.call(this)||this;return t.className="FlowDiagramDataItem",t.values.value={},t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"fromName",{get:function(){return this.properties.fromName},set:function(e){this.setProperty("fromName",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"toName",{get:function(){return this.properties.toName},set:function(e){this.setProperty("toName",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this.properties.color},set:function(e){this.setProperty("color",Object(le.e)(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return this.values.value.value},set:function(e){this.setValue("value",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"link",{get:function(){var e=this;if(!this._link){var t=this.component.links.create();this._link=t,this.addSprite(t),this._disposers.push(new _.b(function(){e.component&&e.component.links.removeValue(t)}))}return this._link},enumerable:!0,configurable:!0}),t}(Z.b),ge=function(e){function t(){var t=e.call(this)||this;t.colors=new de.a,t.className="FlowDiagram",t.nodePadding=20,t.sortBy="none",t.sequencedInterpolation=!0,t.colors.step=2,t.minNodeSize=.02;var i=t.chartContainer.createChild(d.a);i.shouldClone=!1,i.layout="none",i.isMeasured=!1,t.linksContainer=i;var n=t.chartContainer.createChild(d.a);return n.shouldClone=!1,n.layout="none",n.isMeasured=!1,t.nodesContainer=n,t.dataItem=t.createDataItem(),t.dataItem.component=t,t.applyTheme(),t}return r.c(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this.dataItem.dispose()},t.prototype.validateData=function(){var t=this;0==this._parseDataFrom&&this.nodes.clear(),this.sortNodes(),this.colors.reset(),e.prototype.validateData.call(this);var i,n,r=0,a=0;k.each(this.dataItems.iterator(),function(e){var o=e.fromName;o&&((s=t.nodes.getKey(o))||((s=t.nodes.create(o)).name=o,s.chart=t,s.dataItem=e),e.fromNode=s,e.fromNode.outgoingDataItems.push(e));var s,l=e.toName;l&&((s=t.nodes.getKey(l))||((s=t.nodes.create(l)).name=l,s.chart=t,s.dataItem=e),e.toNode=s,e.toNode.incomingDataItems.push(e));if(!e.fromNode){var u=new pe.a;u.opacities=[0,1],e.link.strokeModifier=u}if(!e.toNode){var h=new pe.a;h.opacities=[1,0],e.link.strokeModifier=h}var c=e.value;m.isNumber(c)&&(r+=c,a++,(i>c||!m.isNumber(i))&&(i=c),(n<c||!m.isNumber(n))&&(n=c))});var o="value";this.dataItem.setCalculatedValue(o,n,"high"),this.dataItem.setCalculatedValue(o,i,"low"),this.dataItem.setCalculatedValue(o,r,"sum"),this.dataItem.setCalculatedValue(o,r/a,"average"),this.dataItem.setCalculatedValue(o,a,"count"),k.each(this.nodes.iterator(),function(e){var i=e[1];i.fill instanceof le.a&&(i.color=i.fill),void 0==i.color&&(i.color=t.colors.next()),void 0!=i.dataItem.color&&(i.color=i.dataItem.color),i.dataItem.visible||i.hide(0),t.getNodeValue(i)}),this.sortNodes(),this.feedLegend()},t.prototype.handleDataItemWorkingValueChange=function(e,t){this.invalidate()},t.prototype.sortNodes=function(){"name"==this.sortBy?this._sorted=this.nodes.sortedIterator():"value"==this.sortBy?this._sorted=k.sort(this.nodes.iterator(),function(e,t){return ee.b(te.order(e[1].total,t[1].total))}):this._sorted=this.nodes.iterator()},t.prototype.getNodeValue=function(e){var t=0,i=0;k.each(e.incomingDataItems.iterator(),function(e){var i=e.getWorkingValue("value");m.isNumber(i)&&(t+=i)}),k.each(e.outgoingDataItems.iterator(),function(e){var t=e.getWorkingValue("value");m.isNumber(t)&&(i+=t)}),e.total=t+i,e.totalIncoming=t,e.totalOutgoing=i},t.prototype.changeSorting=function(){this.sortNodes()},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Flow diagram"))},t.prototype.createDataItem=function(){return new ye},Object.defineProperty(t.prototype,"nodePadding",{get:function(){return this.getPropertyValue("nodePadding")},set:function(e){this.setPropertyValue("nodePadding",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sortBy",{get:function(){return this.getPropertyValue("sortBy")},set:function(e){this.setPropertyValue("sortBy",e),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"minNodeSize",{get:function(){return this.getPropertyValue("minNodeSize")},set:function(e){this.setPropertyValue("minNodeSize",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"nodes",{get:function(){if(!this._nodes){var e=this.createNode();e.events.on("hit",function(e){e.target.handleHit(e)}),this._nodes=new Q.c(e),this._disposers.push(new Q.b(this._nodes))}return this._nodes},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new ae;return this._disposers.push(e),e},Object.defineProperty(t.prototype,"links",{get:function(){return this._links||(this._links=new S.e(this.createLink()),this._disposers.push(new S.c(this._links))),this._links},enumerable:!0,configurable:!0}),t.prototype.createLink=function(){var e=new ce;return this._disposers.push(e),e},t.prototype.feedLegend=function(){var e=this.legend;if(e){var t=[];this.nodes.each(function(e,i){t.push(i)}),e.data=t,e.dataFields.name="name"}},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this.nodes.clear()},t}(Z.a);l.b.registeredClasses.FlowDiagram=ge;var fe=i("p9TX"),me=function(e){function t(){var t=e.call(this)||this;t.className="LabelBullet";var i=t.createChild(fe.a);return i.shouldClone=!1,i.verticalCenter="middle",i.horizontalCenter="middle",i.truncate=!0,i.hideOversized=!1,i.maxWidth=500,i.maxHeight=500,i.stroke=Object(le.c)(),i.strokeOpacity=0,i.fill=(new N.a).getFor("text"),t.events.on("maxsizechanged",t.handleMaxSize,t,!1),t.label=i,t.applyTheme(),t}return r.c(t,e),t.prototype.handleMaxSize=function(){this.label.maxWidth=this.maxWidth,this.label.maxHeight=this.maxHeight},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.label.copyFrom(t.label)},t}(se.a);l.b.registeredClasses.LabelBullet=me;var ve=function(e){function t(){var t=e.call(this)||this;t.nextInCoord=0,t.nextOutCoord=0,t.className="SankeyNode",t.width=10,t.height=10;var i=t.createChild(me);i.shouldClone=!1,i.locationX=1,i.locationY=.5,i.label.text="{name}",i.width=150,i.height=150,i.label.horizontalCenter="left",i.label.padding(0,5,0,5),t.nameLabel=i;var n=t.createChild(me);n.shouldClone=!1,n.label.hideOversized=!1,n.locationX=.5,n.locationY=.5,n.width=150,n.height=150,n.label.horizontalCenter="middle",t.valueLabel=n;var r=t.hiddenState;return r.properties.fill=(new N.a).getFor("disabledBackground"),r.properties.opacity=.5,r.properties.visible=!0,t.background.hiddenState.copyFrom(r),t}return r.c(t,e),t.prototype.invalidateLinks=function(){var t=this;e.prototype.invalidateLinks.call(this),this.nextInCoord=0,this.nextOutCoord=0;var i=this.chart;if(i){var n=i.orientation;this._incomingSorted&&k.each(this._incomingSorted,function(e){var r=e.link,a=e.getWorkingValue("value");if(m.isNumber(a)){r.parent=t.chart.linksContainer;var o=void 0,s=void 0,l=void 0;if("horizontal"==n?(o=t.pixelX+t.dx,s=t.nextInCoord+t.pixelY+t.dy,l=0):(s=t.pixelY+t.dy,o=t.nextInCoord+t.pixelX+t.dx,l=90),r.endX=o,r.endY=s,r.startAngle=l,r.endAngle=l,r.gradient.rotation=l,r.linkWidth=a*i.valueHeight,!e.fromNode){"horizontal"==n?(r.maxWidth=200,r.startX=t.pixelX+t.dx-r.maxWidth,r.startY=r.endY):(r.maxHeight=200,r.startX=r.endX,r.startY=t.pixelY+t.dy-r.maxHeight),C.used(r.gradient),r.fill=e.toNode.color;var u=r.gradient.stops.getIndex(0);u&&("gradient"==r.colorMode&&(u.color=t.color),u.opacity=0,r.fill=r.gradient,r.stroke=r.gradient,r.gradient.validate())}t.nextInCoord+=r.linkWidth}}),this._outgoingSorted&&k.each(this._outgoingSorted,function(e){var i=e.link;i.parent=t.chart.linksContainer;var r=e.getWorkingValue("value");if(m.isNumber(r)){var a=void 0,o=void 0,s=void 0;if("horizontal"==n?(s=0,a=t.pixelX+t.pixelWidth+t.dx-1,o=t.nextOutCoord+t.pixelY+t.dy):(s=90,a=t.nextOutCoord+t.pixelX+t.dx,o=t.pixelY+t.pixelHeight+t.dy-1),i.startX=a,i.startY=o,i.startAngle=s,i.endAngle=s,i.gradient.rotation=s,i.linkWidth=r*t.chart.valueHeight,!e.toNode){"horizontal"==n?(i.maxWidth=200,i.endX=t.pixelX+i.maxWidth+t.dx,i.endY=i.startY):(i.maxHeight=200,i.endX=i.startX,i.endY=t.pixelY+i.maxHeight+t.dy),i.opacity=t.opacity;var l=i.gradient.stops.getIndex(1);l&&("gradient"==i.colorMode&&(l.color=t.color),l.opacity=0,i.fill=i.gradient,i.stroke=i.gradient,i.gradient.validate())}t.nextOutCoord+=i.linkWidth}})}this.positionBullet(this.nameLabel),this.positionBullet(this.valueLabel)},t.prototype.positionBullet=function(e){e&&(e.x=this.measuredWidth*e.locationX,e.y=this.measuredHeight*e.locationY)},Object.defineProperty(t.prototype,"level",{get:function(){return this.getPropertyValue("level")},set:function(e){this.setPropertyValue("level",e,!0)},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.nameLabel.copyFrom(t.nameLabel),this.valueLabel.copyFrom(t.valueLabel)},t}(ae);l.b.registeredClasses.SankeyNode=ve;var xe=i("xgTw"),be=i("aFzC"),Pe=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyLink",new N.a,t.tension=.8,t.controlPointDistance=.2,t.startAngle=0,t.endAngle=0,t.linkWidth=0,t.startX=0,t.endX=0,t.startY=0,t.endY=0,t.middleLine=t.createChild(xe.a),t.middleLine.shouldClone=!1,t.middleLine.strokeOpacity=0,t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){if(e.prototype.validate.call(this),!this.isTemplate){var t=this.startX,i=this.startY,n=this.endX,r=this.endY;m.isNumber(n)||(n=t),m.isNumber(r)||(r=i);var a=this.startAngle,o=this.endAngle,s=this.linkWidth,l="",c=t,p=i,d=n,y=r,g=t+s*u.sin(a),f=n+s*u.sin(o),v=i+s*u.cos(a),x=r+s*u.cos(o),b=t+s/2*u.sin(a),P=n+s/2*u.sin(o),C=i+s/2*u.cos(a),A=r+s/2*u.cos(o);this.zIndex=this.zIndex||this.dataItem.index;var D=this.tension+(1-this.tension)*u.sin(a),I=this.tension+(1-this.tension)*u.cos(a);if(this.middleLine.tensionX=D,this.middleLine.tensionY=I,m.isNumber(s)&&m.isNumber(t)&&m.isNumber(n)&&m.isNumber(i)&&m.isNumber(r)){u.round(c,3)==u.round(d,3)&&(d+=.01),u.round(p,3)==u.round(y,3)&&(y+=.01),u.round(g,3)==u.round(f,3)&&(f+=.01),u.round(v,3)==u.round(x,3)&&(x+=.01);var T=Math.min(g,f,c,d),V=Math.min(v,x,p,y),_=Math.max(g,f,c,d),L=Math.max(v,x,p,y);this._bbox={x:T,y:V,width:_-T,height:L-V};var k=this.controlPointDistance,O=c+(d-c)*k*u.cos(a),R=p+(y-p)*k*u.sin(a),S=d-(d-c)*k*u.cos(o),w=y-(y-p)*k*u.sin(o),N=b+(P-b)*k*u.cos(a),j=C+(A-C)*k*u.sin(a),F=P-(P-b)*k*u.cos(o),Y=A-(A-C)*k*u.sin(o),M=u.getAngle({x:O,y:R},{x:S,y:w}),W=(s/u.cos(M)-s)/u.tan(M)*u.cos(a),X=(s/u.sin(M)-s)*u.tan(M)*u.sin(a),H=-W/2+g+(f-g)*k*u.cos(a),B=-X/2+v+(x-v)*k*u.sin(a),z=-W/2+f-(f-g)*k*u.cos(o),E=-X/2+x-(x-v)*k*u.sin(o);this.middleLine.segments=[[{x:b,y:C},{x:N,y:j},{x:F,y:Y},{x:P,y:A}]],O+=W/2,R+=X/2,S+=W/2,w+=X/2,l+=h.moveTo({x:c,y:p}),l+=new be.b(D,I).smooth([{x:c,y:p},{x:O,y:R},{x:S,y:w},{x:d,y:y}]),l+=h.lineTo({x:f,y:x}),l+=new be.b(D,I).smooth([{x:f,y:x},{x:z,y:E},{x:H,y:B},{x:g,y:v}]),l+=h.closePath()}this.link.path=l,this.maskBullets&&(this.bulletsMask.path=l,this.bulletsContainer.mask=this.bulletsMask),this.positionBullets()}},Object.defineProperty(t.prototype,"startX",{get:function(){return this.getPropertyValue("startX")},set:function(e){this.setPropertyValue("startX",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endX",{get:function(){return this.getPropertyValue("endX")},set:function(e){this.setPropertyValue("endX",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startY",{get:function(){return this.getPropertyValue("startY")},set:function(e){this.setPropertyValue("startY",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endY",{get:function(){return this.getPropertyValue("endY")},set:function(e){this.setPropertyValue("endY",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkWidth",{get:function(){return this.getPropertyValue("linkWidth")},set:function(e){this.setPropertyValue("linkWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"controlPointDistance",{get:function(){return this.getPropertyValue("controlPointDistance")},set:function(e){this.setPropertyValue("controlPointDistance",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(e){this.setPropertyValue("tension",e,!0)},enumerable:!0,configurable:!0}),t}(ce);l.b.registeredClasses.SankeyLink=Pe;var Ce=i("1yyj"),Ae=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyDiagramDataItem",t.applyTheme(),t}return r.c(t,e),t}(ye),De=function(e){function t(){var t=e.call(this)||this;return t.className="SankeyDiagram",t.orientation="horizontal",t.nodeAlign="middle",t.nodesContainer.width=Object(o.c)(100),t.nodesContainer.height=Object(o.c)(100),t.linksContainer.width=Object(o.c)(100),t.linksContainer.height=Object(o.c)(100),t.applyTheme(),t}return r.c(t,e),t.prototype.validateData=function(){var t=this;e.prototype.validateData.call(this),this._levelCount=0,this.nodes.each(function(e,t){t.level=void 0}),this.nodes.each(function(e,i){i.level=t.getNodeLevel(i,0),t._levelCount=u.max(t._levelCount,i.level)})},t.prototype.getNodeLevel=function(e,t){var i=this,n=[t];return k.each(e.incomingDataItems.iterator(),function(e){e.fromNode&&(m.isNumber(e.fromNode.level)?n.push(e.fromNode.level+1):n.push(i.getNodeLevel(e.fromNode,t+1)))}),Math.max.apply(Math,r.f(n))},t.prototype.calculateValueHeight=function(){var e=this;this._levelSum={},this._levelNodesCount={},this.maxSum=0;var t,i,n,r=this.dataItem.values.value.sum;k.each(this._sorted,function(t){var i=t[1];e.getNodeValue(i)}),this.nodes.each(function(t,i){var n=i.level,a=Math.max(i.totalIncoming,i.totalOutgoing);a/r<e.minNodeSize&&(a=r*e.minNodeSize),m.isNumber(e._levelSum[n])?e._levelSum[n]+=a:e._levelSum[n]=a,m.isNumber(e._levelNodesCount[n])?e._levelNodesCount[n]++:e._levelNodesCount[n]=1}),t="horizontal"==this.orientation?this.chartContainer.maxHeight-1:this.chartContainer.maxWidth-1,ne.each(this._levelSum,function(r,a){var o=a,s=e._levelNodesCount[r],l=(t-(s-1)*e.nodePadding)/o;(n>l||!m.isNumber(n))&&(n=l,e.maxSum=o,i=m.toNumber(r))}),this._maxSumLevel=i;var a=this._levelNodesCount[this._maxSumLevel],o=(t-(a-1)*this.nodePadding)/this.maxSum;if(m.isNumber(this.valueHeight)){var s=void 0;try{s=this._heightAnimation.animationOptions[0].to}catch(e){}if(s!=o){var l=this.interpolationDuration;try{l=this.nodes.template.states.getKey("active").transitionDuration}catch(e){}this._heightAnimation=new Ce.a(this,{property:"valueHeight",from:this.valueHeight,to:o},l,this.interpolationEasing).start(),this._disposers.push(this._heightAnimation)}}else this.valueHeight=o},t.prototype.validate=function(){var t=this;e.prototype.validate.call(this),this.calculateValueHeight();var i=this.nodesContainer,n={},r=this._levelNodesCount[this._maxSumLevel],a=this.dataItem.values.value.sum;k.each(this._sorted,function(e){var o,s,l,u=e[1],h=u.level,c=0,p=t._levelNodesCount[h];switch(t.nodeAlign){case"bottom":c=(t.maxSum-t._levelSum[h])*t.valueHeight-(p-r)*t.nodePadding;break;case"middle":c=(t.maxSum-t._levelSum[h])*t.valueHeight/2-(p-r)*t.nodePadding/2}u.parent=i;var d=Math.max(u.totalIncoming,u.totalOutgoing);if(d/a<t.minNodeSize&&(d=a*t.minNodeSize),"horizontal"==t.orientation){s=(o=(t.innerWidth-u.pixelWidth)/t._levelCount)*u.level,l=n[h]||c;var y=d*t.valueHeight;u.height=y,u.minX=s,u.maxX=s,n[h]=l+y+t.nodePadding}else{o=(t.innerHeight-u.pixelHeight)/t._levelCount,s=n[h]||c,l=o*u.level;var g=d*t.valueHeight;u.width=g,u.minY=l,u.maxY=l,n[h]=s+g+t.nodePadding}u.x=s,u.y=l})},t.prototype.showReal=function(t){var i=this;if(this.interpolationDuration>0){var n=this.nodesContainer,r=0;k.each(this.links.iterator(),function(e){e.hide(0)}),k.each(this._sorted,function(e){var t,a=e[1];"horizontal"==i.orientation?(a.dx=-(n.pixelWidth-a.pixelWidth)/i._levelCount,t="dx"):(a.dy=-(n.pixelHeight-a.pixelHeight)/i._levelCount,t="dy");var o=0,s=i.interpolationDuration;i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*r+s*r/k.length(i.nodes.iterator())),a.opacity=0,a.invalidateLinks(),a.animate([{property:"opacity",from:0,to:1},{property:t,to:0}],i.interpolationDuration,i.interpolationEasing).delay(o),k.each(a.outgoingDataItems.iterator(),function(e){var t=e.link.show(i.interpolationDuration);t&&!t.isFinished()&&t.delay(o)}),k.each(a.incomingDataItems.iterator(),function(e){if(!e.fromNode){var t=e.link.show(i.interpolationDuration);t&&!t.isFinished()&&t.delay(o)}}),r++})}return e.prototype.showReal.call(this)},t.prototype.changeSorting=function(){var e=this;this.sortNodes();var t={};k.each(this._sorted,function(i){var n,r,a=i[1],o=a.level,s=(e.maxSum-e._levelSum[o])*e.valueHeight/2;"horizontal"==e.orientation?(n="y",r=a.pixelHeight):(n="x",r=a.pixelWidth),a.animate({property:n,to:t[o]||s},e.interpolationDuration,e.interpolationEasing),t[o]=(t[o]||s)+r+e.nodePadding,a.invalidateLinks()})},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sankey diagram"))},t.prototype.createDataItem=function(){return new Ae},Object.defineProperty(t.prototype,"nodeAlign",{get:function(){return this.getPropertyValue("nodeAlign")},set:function(e){this.setPropertyValue("nodeAlign",e),this.changeSorting()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0);var t=this.nodes.template.nameLabel;"vertical"==e?(this.nodes.template.width=void 0,t.label.horizontalCenter="middle",t.locationX=.5):(this.nodes.template.height=void 0,t.label.horizontalCenter="left",t.locationX=1)},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new ve;return this._disposers.push(e),e},t.prototype.createLink=function(){var e=new Pe;return this._disposers.push(e),e},Object.defineProperty(t.prototype,"valueHeight",{get:function(){return this._valueHeight},set:function(e){e!=this._valueHeight&&(this._valueHeight=e,this.invalidate())},enumerable:!0,configurable:!0}),t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this._sorted=this.nodes.iterator()},t}(ge);l.b.registeredClasses.SankeyDiagram=De;var Ie=i("Inf5"),Te=function(e){function t(){var t=e.call(this)||this;t.className="ChordNode";var i=t.createChild(P.a);i.location=.5,i.radius=5,i.text="{name}",i.zIndex=1,i.shouldClone=!1,t.label=i,t.layout="none",t.events.on("positionchanged",t.updateRotation,t,!1),t.isMeasured=!1,t.slice=t.createChild(Ie.a),t.slice.isMeasured=!1;var n=t.hiddenState;return n.properties.fill=(new N.a).getFor("disabledBackground"),n.properties.opacity=.5,n.properties.visible=!0,t.setStateOnChildren=!1,t.slice.hiddenState.properties.visible=!0,t.adapter.add("tooltipX",function(e,t){return t.slice.ix*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t.adapter.add("tooltipY",function(e,t){return t.slice.iy*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t}return r.c(t,e),t.prototype.invalidateLinks=function(){var t=this;e.prototype.invalidateLinks.call(this);var i=this.label,n=this.slice,r=this.chart;if(r&&n){var a=this.total,o=n.arc,s=n.startAngle;this.children.each(function(e){if(e instanceof se.a){var t=e.locationX;m.isNumber(t)||(t=.5);var i=e.locationY;m.isNumber(i)||(i=1);var r=s+o*t,a=i*n.radius;e.x=a*u.cos(r),e.y=a*u.sin(r)}});var l=s+o*i.location,h=s+(1-a/this.adjustedTotal)*o*.5;m.isNaN(h)&&(h=s),i.fixPosition(l,n.radius),this.nextAngle=h,this._outgoingSorted&&k.each(this._outgoingSorted,function(e){var i=e.link;i.parent=t.chart.linksContainer;var a=e.getWorkingValue("value");if(m.isNumber(a)){if(r.nonRibbon){var l=i.percentWidth;m.isNumber(l)||(l=5),l/=100,i.startAngle=s+o/2-o/2*l,i.arc=o*l}else i.arc=a*r.valueAngle,i.startAngle=t.nextAngle,t.nextAngle+=i.arc;e.toNode||(i.endAngle=i.startAngle),i.radius=n.pixelInnerRadius}}),this._incomingSorted&&k.each(this._incomingSorted,function(e){var i=e.link;if(i.radius=n.pixelInnerRadius,r.nonRibbon){var a=i.percentWidth;m.isNumber(a)||(a=5),a/=100,i.endAngle=s+o/2-o/2*a,i.arc=o*a}else{i.endAngle=t.nextAngle;var l=e.getWorkingValue("value");m.isNumber(l)&&(i.arc=l*r.valueAngle,t.nextAngle+=i.arc)}e.fromNode||(i.startAngle=i.endAngle)})}},t.prototype.updateRotation=function(){var e=this.slice,t=this.trueStartAngle+e.arc/2,i=e.radius,n=i*u.cos(t),r=i*u.sin(t),a=u.getAngle({x:n+this.pixelX,y:r+this.pixelY});e.startAngle=this.trueStartAngle+(a-t),this.dx=-this.pixelX,this.dy=-this.pixelY},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.label.copyFrom(t.label),this.slice.copyFrom(t.slice)},t}(ae);l.b.registeredClasses.ChordNode=Te;var Ve=function(e){function t(){var t=e.call(this)||this;return t.className="QuadraticCurve",t.element=t.paper.add("path"),t.pixelPerfect=!1,t.fill=Object(le.c)(),t.applyTheme(),t}return r.c(t,e),t.prototype.draw=function(){if(m.isNumber(this.x1+this.x2+this.y1+this.y2+this.cpx+this.cpy)){var e={x:this.x1,y:this.y1},t={x:this.x2,y:this.y2},i={x:this.cpx,y:this.cpy},n=h.moveTo(e)+h.quadraticCurveTo(t,i);this.path=n}},Object.defineProperty(t.prototype,"cpx",{get:function(){return this.getPropertyValue("cpx")},set:function(e){this.setPropertyValue("cpx",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cpy",{get:function(){return this.getPropertyValue("cpy")},set:function(e){this.setPropertyValue("cpy",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e){var t={x:this.x1,y:this.y1},i={x:this.cpx,y:this.cpy},n={x:this.x2,y:this.y2},r=u.getPointOnQuadraticCurve(t,n,i,e),a=u.getPointOnQuadraticCurve(t,n,i,e+.001);return{x:r.x,y:r.y,angle:u.getAngle(r,a)}},t}(he.a),_e=function(e){function t(){var t=e.call(this)||this;return t.className="ChordLink",t.middleLine=t.createChild(Ve),t.middleLine.shouldClone=!1,t.middleLine.strokeOpacity=0,t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){if(e.prototype.validate.call(this),!this.isTemplate){var t=this.startAngle,i=this.endAngle,n=this.arc,r=this.radius,a=this.dataItem.fromNode,o=this.dataItem.toNode,s=0,l=0;a&&(s=a.pixelX+a.dx,l=a.pixelY+a.dy);var c=0,p=0;if(o&&(c=o.pixelX+o.dx,p=o.pixelY+o.dy),r>0){var d=r*u.cos(t)+s,y=r*u.sin(t)+l,g=r*u.cos(i)+c,f=r*u.sin(i)+p,m={x:0,y:0},v=h.moveTo({x:d,y:y});v+=h.arcTo(t,n,r),v+=h.quadraticCurveTo({x:g,y:f},m),v+=h.arcTo(i,n,r),v+=h.quadraticCurveTo({x:d,y:y},m),this.link.path=n>0?v:"",this.maskBullets&&(this.bulletsMask.path=v,this.bulletsContainer.mask=this.bulletsMask);var x=t+n/2,b=i+n/2,P=this.middleLine;P.x1=r*u.cos(x)+s,P.y1=r*u.sin(x)+l,P.x2=r*u.cos(b)+c,P.y2=r*u.sin(b)+p,P.cpx=0,P.cpy=0,P.stroke=this.fill,this.positionBullets()}}},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPropertyValue("radius",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"arc",{get:function(){return this.getPropertyValue("arc")},set:function(e){this.setPropertyValue("arc",e,!0)},enumerable:!0,configurable:!0}),t}(ce);l.b.registeredClasses.ChordLink=_e;var Le=function(e){function t(){var t=e.call(this)||this;return t.className="ChordDiagramDataItem",t.applyTheme(),t}return r.c(t,e),t}(ye),ke=function(e){function t(){var t=e.call(this)||this;t.valueAngle=0,t.className="ChordDiagram",t.startAngle=-90,t.endAngle=270,t.radius=Object(o.c)(80),t.innerRadius=-15,t.nodePadding=5;var i=t.chartContainer.createChild(d.a);return i.align="center",i.valign="middle",i.shouldClone=!1,i.layout="absolute",t.chordContainer=i,t.nodesContainer.parent=i,t.linksContainer.parent=i,t.chartContainer.events.on("maxsizechanged",t.invalidate,t,!1),t.applyTheme(),t}return r.c(t,e),t.prototype.validate=function(){var t=this,i=this.chartContainer,n=this.endAngle,r=this.startAngle+this.nodePadding/2,a=u.getArcRect(this.startAngle,this.endAngle,1);a=u.getCommonRectangle([a,{x:0,y:0,width:0,height:0}]);var o=Math.min(i.innerWidth/a.width,i.innerHeight/a.height);m.isNumber(o)||(o=0);var s=C.relativeRadiusToValue(this.radius,o),l=C.relativeRadiusToValue(this.innerRadius,s,!0),h=this.dataItem.values.value.sum,c=0,p=0;k.each(this._sorted,function(e){var i=e[1];t.getNodeValue(i),c++;var n=i.total;i.total/h<t.minNodeSize&&(n=h*t.minNodeSize),p+=n}),this.valueAngle=(n-this.startAngle-this.nodePadding*c)/p,k.each(this._sorted,function(e){var i=e[1],a=i.slice;a.radius=s,a.innerRadius=l;var o,u=i.total;i.total/h<t.minNodeSize&&(u=h*t.minNodeSize),i.adjustedTotal=u,o=t.nonRibbon?(n-t.startAngle)/c-t.nodePadding:t.valueAngle*u,a.arc=o,a.startAngle=r,i.trueStartAngle=r,i.parent=t.nodesContainer,i.validate(),r+=o+t.nodePadding}),this.chordContainer.definedBBox={x:s*a.x,y:s*a.y,width:s*a.width,height:s*a.height},this.chordContainer.invalidateLayout(),e.prototype.validate.call(this)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Chord diagram"))},t.prototype.createDataItem=function(){return new Le},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"nonRibbon",{get:function(){return this.getPropertyValue("nonRibbon")},set:function(e){this.setPropertyValue("nonRibbon",e,!0),this.links.template.middleLine.strokeOpacity=1,this.links.template.link.fillOpacity=0},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new Te;return this._disposers.push(e),e},t.prototype.createLink=function(){var e=new _e;return this._disposers.push(e),e},t}(ge);l.b.registeredClasses.ChordDiagram=ke;var Oe=i("pR7v"),Re=i("5vid"),Se=function(e){function t(){var t=e.call(this)||this;return t.className="TreeMapSeriesDataItem",t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"parentName",{get:function(){var e=this.treeMapDataItem;if(e&&e.parent)return e.parent.name},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){var e=this.treeMapDataItem;if(e)return e.value},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"treeMapDataItem",{get:function(){return this._dataContext},enumerable:!0,configurable:!0}),t.prototype.hide=function(t,i,n,r){var a=this.treeMapDataItem;return a&&a.hide(t),e.prototype.hide.call(this,t,i,n,r)},t.prototype.show=function(t,i,n){var r=this.treeMapDataItem;return r&&r.show(t,i,n),e.prototype.show.call(this,t,i,n)},t}(Re.b),we=function(e){function t(){var t=e.call(this)||this;t.className="TreeMapSeries",t.applyTheme(),t.fillOpacity=1,t.strokeOpacity=1,t.minBulletDistance=0,t.columns.template.tooltipText="{parentName} {name}: {value}",t.columns.template.configField="config";var i=new N.a;return t.stroke=i.getFor("background"),t.dataFields.openValueX="x0",t.dataFields.valueX="x1",t.dataFields.openValueY="y0",t.dataFields.valueY="y1",t.sequencedInterpolation=!1,t.showOnInit=!1,t.columns.template.pixelPerfect=!1,t}return r.c(t,e),t.prototype.processDataItem=function(t,i){i.seriesDataItem=t,e.prototype.processDataItem.call(this,t,i)},t.prototype.createDataItem=function(){return new Se},t.prototype.show=function(t){var i=this.defaultState.transitionDuration;return m.isNumber(t)&&(i=t),this.dataItems.each(function(e){e.show(t)}),e.prototype.showReal.call(this,i)},t.prototype.hide=function(t){var i=this.defaultState.transitionDuration;m.isNumber(t)&&(i=t);var n=e.prototype.hideReal.call(this,i);return this.dataItems.each(function(e){e.hide(t)}),n},t.prototype.processValues=function(){},t.prototype.getStartLocation=function(e){return 0},t.prototype.getEndLocation=function(e){return 1},t.prototype.dataChangeUpdate=function(){},t.prototype.processConfig=function(t){t&&(m.hasValue(t.dataFields)&&m.isObject(t.dataFields)||(t.dataFields={})),e.prototype.processConfig.call(this,t)},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n=e.createChild(ie.a);n.shouldClone=!1,ne.copyProperties(this,n,J.b),n.padding(0,0,0,0),n.width=t,n.height=i;var r=e.dataItem;r.color=n.fill,r.colorOrig=n.fill},t}(Re.a);l.b.registeredClasses.TreeMapSeries=we,l.b.registeredClasses.TreeMapSeriesDataItem=Se;var Ne=i("qCRI"),je=i("hJ5i"),Fe=function(e){function t(){var t=e.call(this)||this;return t.rows=[],t.className="TreeMapDataItem",t.values.value={},t.values.x0={},t.values.y0={},t.values.x1={},t.values.y1={},t.hasChildren.children=!0,t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"legendDataItem",{get:function(){return this._legendDataItem},set:function(e){this._legendDataItem=e,e.label&&(e.label.dataItem=this),e.valueLabel&&(e.valueLabel.dataItem=this)},enumerable:!0,configurable:!0}),t.prototype.getDuration=function(){return 0},Object.defineProperty(t.prototype,"value",{get:function(){var e=0;return this.children&&0!=this.children.length?k.each(this.children.iterator(),function(t){var i=t.value;m.isNumber(i)&&(e+=i)}):e=this.values.value.workingValue,e},set:function(e){this.setValue("value",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"percent",{get:function(){return this.parent?this.value/this.parent.value*100:100},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x0",{get:function(){return this.values.x0.value},set:function(e){this.setValue("x0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x1",{get:function(){return this.values.x1.value},set:function(e){this.setValue("x1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y0",{get:function(){return this.values.y0.value},set:function(e){this.setValue("y0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y1",{get:function(){return this.values.y1.value},set:function(e){this.setValue("y1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties.children},set:function(e){this.setProperty("children",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"level",{get:function(){return this.parent?this.parent.level+1:0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){var e=this.properties.color;return void 0==e&&this.parent&&(e=this.parent.color),void 0==e&&this.component&&(e=this.component.colors.getIndex(this.component.colors.step*this.index)),e},set:function(e){this.setProperty("color",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"fill",{get:function(){return this.color},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"series",{get:function(){return this._series},set:function(e){e!=this._series&&(this._series&&(this.component.series.removeValue(this._series),this._series.dispose()),this._series=e,this._disposers.push(e))},enumerable:!0,configurable:!0}),t.prototype.hide=function(t,i,n,r){return this.setWorkingValue("value",0),this.children&&this.children.each(function(e){e.hide(t,i,n,r)}),e.prototype.hide.call(this,t,i,n,r)},t.prototype.show=function(t,i,n){return this.setWorkingValue("value",this.values.value.value),this.children&&this.children.each(function(e){e.show(t,i,n)}),e.prototype.show.call(this,t,i,n)},t}(a.b),Ye=function(e){function t(){var t=e.call(this)||this;t.layoutAlgorithm=t.squarify,t.zoomable=!0,t.className="TreeMap",t._usesData=!0,t.maxLevels=2,t.currentLevel=0,t.hideParentColumns=!1,t.colors=new de.a,t.sorting="descending";var i=t.xAxes.push(new Oe.a);i.title.disabled=!0,i.strictMinMax=!0;var n=i.renderer;n.inside=!0,n.labels.template.disabled=!0,n.ticks.template.disabled=!0,n.grid.template.disabled=!0,n.axisFills.template.disabled=!0,n.minGridDistance=100,n.line.disabled=!0,n.baseGrid.disabled=!0;var r=t.yAxes.push(new Oe.a);r.title.disabled=!0,r.strictMinMax=!0;var a=r.renderer;a.inside=!0,a.labels.template.disabled=!0,a.ticks.template.disabled=!0,a.grid.template.disabled=!0,a.axisFills.template.disabled=!0,a.minGridDistance=100,a.line.disabled=!0,a.baseGrid.disabled=!0,a.inversed=!0,t.xAxis=i,t.yAxis=r;var o=new we;return t.seriesTemplates=new Q.c(o),o.virtualParent=t,t._disposers.push(new Q.b(t.seriesTemplates)),t._disposers.push(o),t.zoomOutButton.events.on("hit",function(){t.zoomToChartDataItem(t._homeDataItem)},void 0,!1),t.seriesTemplates.events.on("insertKey",function(e){e.newValue.isTemplate=!0},void 0,!1),t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"navigationBar",{get:function(){return this._navigationBar},set:function(e){var t=this;this._navigationBar!=e&&(this._navigationBar=e,e.parent=this,e.toBack(),e.links.template.events.on("hit",function(e){var i=e.target.dataItem.dataContext;i.isDisposed()||(t.zoomToChartDataItem(i),t.createTreeSeries(i))},void 0,!0),this._disposers.push(e))},enumerable:!0,configurable:!0}),t.prototype.validateData=function(){this.series.clear(),this._tempSeries=[],e.prototype.validateData.call(this),this._homeDataItem&&this._homeDataItem.dispose();var t=this.dataItems.template.clone();this._homeDataItem=t,k.each(this.dataItems.iterator(),function(e){e.parent=t}),t.children=this.dataItems,t.x0=0,t.y0=0,t.name=this._homeText;var i=10*Math.round(1e3*this.pixelHeight/this.pixelWidth/10)||1e3;t.x1=1e3,t.y1=i,this.xAxis.min=0,this.xAxis.max=1e3,this.xAxis.getMinMax(),this.yAxis.min=0,this.yAxis.max=i,this.yAxis.getMinMax(),this.layoutItems(t),this.createTreeSeries(t)},t.prototype.layoutItems=function(e,t){if(e){var i=e.children;t||(t=this.sorting),"ascending"==t&&i.values.sort(function(e,t){return e.value-t.value}),"descending"==t&&i.values.sort(function(e,t){return t.value-e.value}),this._updateDataItemIndexes(0),this.layoutAlgorithm(e);for(var n=0,r=i.length;n<r;n++){var a=i.getIndex(n);a.children&&this.layoutItems(a)}}},t.prototype.createTreeSeries=function(e){var t=this;this._tempSeries=[];for(var i=[e],n=e.parent;void 0!=n;)this.initSeries(n),i.push(n),n=n.parent;i.reverse(),this.navigationBar&&(this.navigationBar.data=i),this.createTreeSeriesReal(e),je.each(this._tempSeries,function(e){-1==t.series.indexOf(e)&&t.series.push(e),e.zIndex=e.level})},t.prototype.createTreeSeriesReal=function(e){if(e.children&&e.level<this.currentLevel+this.maxLevels){this.initSeries(e);for(var t=0;t<e.children.length;t++){var i=e.children.getIndex(t);i.children&&this.createTreeSeriesReal(i)}}},t.prototype.setData=function(t){this.currentLevel=0,this.currentlyZoomed=void 0,this.xAxis.start=0,this.xAxis.end=1,this.yAxis.start=0,this.yAxis.end=1,e.prototype.setData.call(this,t)},t.prototype.seriesAppeared=function(){return!0},t.prototype.initSeries=function(e){var t=this;if(!e.series){var i=void 0,n=this.seriesTemplates.getKey(e.level.toString());(i=n?n.clone():this.series.create()).dataItem.dataContext=e,i.name=e.name,i.parentDataItem=e,e.series=i;var r=e.level;i.level=r;var a=e.dataContext;a&&(i.config=a.config),this.dataUsers.removeValue(i),i.data=e.children.values,i.fill=e.color,i.columnsContainer.hide(0),i.bulletsContainer.hide(0),i.columns.template.adapter.add("fill",function(e,t){var i=t.dataItem;if(i){var n=i.treeMapDataItem;if(n)return t.fill=n.color,t.adapter.remove("fill"),n.color}}),this.zoomable&&(e.level>this.currentLevel||e.children&&e.children.length>0)&&(i.columns.template.cursorOverStyle=Ne.a.pointer,this.zoomable&&i.columns.template.events.on("hit",function(i){var n=i.target.dataItem;e.level>t.currentLevel?t.zoomToChartDataItem(n.treeMapDataItem.parent):t.zoomToSeriesDataItem(n)},this,void 0))}this._tempSeries.push(e.series)},t.prototype.toggleBullets=function(e){var t=this;k.each(this.series.iterator(),function(i){-1==t._tempSeries.indexOf(i)?(i.columnsContainer.hide(),i.bulletsContainer.hide(e)):(i.columnsContainer.show(),i.bulletsContainer.show(e),i.dataItems.each(function(e){e.bullets.each(function(e,t){t.show()})}),i.level<t.currentLevel?(t.hideParentColumns&&i.columnsContainer.hide(),i.bulletsContainer.hide(e)):i.level==t.currentLevel&&t.maxLevels>1&&i.dataItems.each(function(e){e.treeMapDataItem.children&&e.bullets.each(function(e,t){t.hide()})}))})},t.prototype.zoomToSeriesDataItem=function(e){this.zoomToChartDataItem(e.treeMapDataItem)},t.prototype.zoomToChartDataItem=function(e){var t=this,i=this.zoomOutButton;if(i&&(e!=this._homeDataItem?i.show():i.hide()),e&&e.children){this.xAxis.zoomToValues(e.x0,e.x1),this.yAxis.zoomToValues(e.y0,e.y1),this.currentLevel=e.level,this.currentlyZoomed=e,this.createTreeSeries(e);var n=this.xAxis.rangeChangeAnimation||this.yAxis.rangeChangeAnimation;!n||n.isDisposed()||n.isFinished()?this.toggleBullets():(this._dataDisposers.push(n),n.events.once("animationended",function(){t.toggleBullets()}))}},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("TreeMap chart"))},t.prototype.createDataItem=function(){return new Fe},Object.defineProperty(t.prototype,"maxLevels",{get:function(){return this.getPropertyValue("maxLevels")},set:function(e){this.setPropertyValue("maxLevels",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentLevel",{get:function(){return this.getPropertyValue("currentLevel")},set:function(e){this.setPropertyValue("currentLevel",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hideParentColumns",{get:function(){return this.getPropertyValue("hideParentColumns")},set:function(e){this.setPropertyValue("hideParentColumns",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sorting",{get:function(){return this.getPropertyValue("sorting")},set:function(e){this.setPropertyValue("sorting",e,!0)},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new we},Object.defineProperty(t.prototype,"homeText",{get:function(){return this._homeText},set:function(e){this._homeText=e,this._homeDataItem&&(this._homeDataItem.name=this._homeText)},enumerable:!0,configurable:!0}),t.prototype.processConfig=function(t){if(t){if(m.hasValue(t.layoutAlgorithm)&&m.isString(t.layoutAlgorithm))switch(t.layoutAlgorithm){case"squarify":t.layoutAlgorithm=this.squarify;break;case"binaryTree":t.layoutAlgorithm=this.binaryTree;break;case"slice":t.layoutAlgorithm=this.slice;break;case"dice":t.layoutAlgorithm=this.dice;break;case"sliceDice":t.layoutAlgorithm=this.sliceDice;break;default:delete t.layoutAlgorithm}m.hasValue(t.navigationBar)&&!m.hasValue(t.navigationBar.type)&&(t.navigationBar.type="NavigationBar"),e.prototype.processConfig.call(this,t)}},t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.layoutItems(this.currentlyZoomed)},t.prototype.validateDataItems=function(){e.prototype.validateDataItems.call(this),this.layoutItems(this._homeDataItem),k.each(this.series.iterator(),function(e){e.validateRawData()}),this.zoomToChartDataItem(this._homeDataItem)},t.prototype.binaryTree=function(e){var t,i,n=e.children,r=n.length,a=new Array(r+1);for(a[0]=i=t=0;t<r;++t)a[t+1]=i+=n.getIndex(t).value;!function e(t,i,r,o,s,l,u){if(t>=i-1){var h=n.getIndex(t);return h.x0=o,h.y0=s,h.x1=l,void(h.y1=u)}var c=a[t],p=r/2+c,d=t+1,y=i-1;for(;d<y;){var g=d+y>>>1;a[g]<p?d=g+1:y=g}p-a[d-1]<a[d]-p&&t+1<d&&--d;var f=a[d]-c,m=r-f;if(l-o>u-s){var v=(o*m+l*f)/r;e(t,d,f,o,s,v,u),e(d,i,m,v,s,l,u)}else{var x=(s*m+u*f)/r;e(t,d,f,o,s,l,x),e(d,i,m,o,x,l,u)}}(0,r,e.value,e.x0,e.y0,e.x1,e.y1)},t.prototype.slice=function(e){for(var t,i=e.x0,n=e.x1,r=e.y0,a=e.y1,o=e.children,s=-1,l=o.length,u=e.value&&(a-r)/e.value;++s<l;)(t=o.getIndex(s)).x0=i,t.x1=n,t.y0=r,r+=t.value*u,t.y1=r},t.prototype.dice=function(e){for(var t,i=e.x0,n=e.x1,r=e.y0,a=e.y1,o=e.children,s=-1,l=o.length,u=e.value&&(n-i)/e.value;++s<l;)(t=o.getIndex(s)).y0=r,t.y1=a,t.x0=i,i+=t.value*u,t.x1=i},t.prototype.sliceDice=function(e){1&e.level?this.slice(e):this.dice(e)},t.prototype.squarify=function(e){for(var t,i,n,r,a,o,s,l,u,h,c=(1+Math.sqrt(5))/2,p=e.x0,d=e.x1,y=e.y0,g=e.y1,f=e.children,m=0,v=0,x=f.length,b=e.value;m<x;){i=d-p,n=g-y;do{r=f.getIndex(v++).value}while(!r&&v<x);for(a=o=r,h=r*r*(u=Math.max(n/i,i/n)/(b*c)),l=Math.max(o/h,h/a);v<x;++v){if(r+=t=f.getIndex(v).value,t<a&&(a=t),t>o&&(o=t),h=r*r*u,(s=Math.max(o/h,h/a))>l){r-=t;break}l=s}var P=this.dataItems.template.clone();P.value=r,P.dice=i<n,P.children=f.slice(m,v),P.x0=p,P.y0=y,P.x1=d,P.y1=g,P.dice?(P.y1=b?y+=n*r/b:g,this.dice(P)):(P.x1=b?p+=i*r/b:d,this.slice(P)),b-=r,m=v}},t.prototype.handleSeriesAdded2=function(){},t.prototype.handleDataItemValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.handleDataItemWorkingValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.getLegendLevel=function(e){if(e&&e.children)return e.children.length>1?e:1==e.children.length?this.getLegendLevel(e.children.getIndex(0)):e},t.prototype.feedLegend=function(){var e=this.legend;if(e){e.dataFields.name="name";var t=this.getLegendLevel(this._homeDataItem);if(t){var i=[];t.children.each(function(e){i.push(e)}),e.data=i}}},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this._homeDataItem=void 0,this.series.clear(),this.navigationBar&&this.navigationBar.disposeData(),this.xAxis.disposeData(),this.yAxis.disposeData()},t.prototype.getExporting=function(){var t=this,i=e.prototype.getExporting.call(this);return i.adapter.add("formatDataFields",function(e){return"csv"!=e.format&&"xlsx"!=e.format||m.hasValue(t.dataFields.children)&&delete e.dataFields[t.dataFields.children],e}),i},t}(a.a);l.b.registeredClasses.TreeMap=Ye;var Me=i("k6kv"),We=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.className="AxisRendererX3D",t._disposers.push(t._chart),t.applyTheme(),t}return r.c(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var r=this.chart.dx3D||0,a=this.chart.dy3D||0,o=this.getHeight();e.path=h.moveTo({x:r,y:a})+h.lineTo({x:r,y:o+a})+h.lineTo({x:0,y:o})}this.positionItem(e,n),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.getHeight(),i=this.chart.dx3D||0,n=this.chart.dy3D||0;this.baseGrid.path=h.moveTo({x:i,y:n})+h.lineTo({x:0,y:0})+h.lineTo({x:0,y:t})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(Me.a);l.b.registeredClasses.AxisRendererX3D=We;var Xe=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.className="AxisRendererY3D",t._disposers.push(t._chart),t.applyTheme(),t}return r.c(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var n=this.positionToPoint(t);if(e.element){var r=this.chart.dx3D||0,a=this.chart.dy3D||0,o=this.getWidth();e.path=h.moveTo({x:0,y:0})+h.lineTo({x:r,y:a})+h.lineTo({x:o+r,y:a})}this.positionItem(e,n),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.chart.dx3D||0,i=this.chart.dy3D||0,n=this.getWidth();this.baseGrid.path=h.moveTo({x:0,y:0})+h.lineTo({x:n,y:0})+h.lineTo({x:n+t,y:i})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(I.a),He=i("DG6Q"),Be=i("Mr4Y"),ze=function(e){function t(){var t=e.call(this)||this;return t.className="Column3D",t}return r.c(t,e),t.prototype.createAssets=function(){this.column3D=this.createChild(Be.a),this.column3D.shouldClone=!1,this.column3D.strokeOpacity=0,this.column=this.column3D},t.prototype.validate=function(){e.prototype.validate.call(this),this.column3D&&(this.column3D.width=this.pixelWidth,this.column3D.height=this.pixelHeight,this.column3D.invalid&&this.column3D.validate())},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.column3D&&this.column3D.copyFrom(t.column3D)},t.prototype.setFill=function(t){e.prototype.setFill.call(this,t),this.column.fill=t},t}(He.a);l.b.registeredClasses.Column3D=ze;var Ee=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3DDataItem",t.applyTheme(),t}return r.c(t,e),t}(Re.b),Ge=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3D",t.columns.template.column3D.applyOnClones=!0,t.columns.template.hiddenState.properties.visible=!0,t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"columnsContainer",{get:function(){var e=this.chart;return e&&e.columnsContainer&&"vertical"!=e.leftAxesContainer.layout&&"vertical"!=e.rightAxesContainer.layout&&"horizontal"!=e.bottomAxesContainer.layout&&"horizontal"!=e.topAxesContainer.layout?e.columnsContainer:this._columnsContainer},enumerable:!0,configurable:!0}),t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),t.column&&(t.column.dx=this.dx,t.column.dy=this.dy)},t.prototype.validateDataElements=function(){e.prototype.validateDataElements.call(this),this.chart&&this.chart.invalidateLayout()},t.prototype.createColumnTemplate=function(){return new ze},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0),this.columns.template.column3D.depth=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.columns.template.column3D.angle=e},enumerable:!0,configurable:!0}),t}(Re.a);l.b.registeredClasses.ColumnSeries3D=Ge,l.b.registeredClasses.ColumnSeries3DDataItem=Ee;var qe=function(e){function t(){var t=e.call(this)||this;return t.className="XYChart3DDataItem",t.applyTheme(),t}return r.c(t,e),t}(a.b),Ue=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=We,t._axisRendererY=Xe,t.className="XYChart3D",t.depth=30,t.angle=30;var i=t.seriesContainer.createChild(d.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",t.columnsContainer=i,t.columnsContainer.mask=t.createChild(J.a),t.applyTheme(),t}return r.c(t,e),t.prototype.updateSeriesMasks=function(){if(e.prototype.updateSeriesMasks.call(this),C.isIE()){var t=this.columnsContainer,i=t.mask;t.mask=void 0,t.mask=i}},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dx3D",{get:function(){return u.cos(this.angle)*this.depth},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dy3D",{get:function(){return-u.sin(this.angle)*this.depth},enumerable:!0,configurable:!0}),t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.fixColumns()},t.prototype.fixLayout=function(){this.chartContainer.marginTop=-this.dy3D,this.chartContainer.paddingRight=this.dx3D,this.scrollbarX&&(this.scrollbarX.dy=this.dy3D,this.scrollbarX.dx=this.dx3D),this.scrollbarY&&(this.scrollbarY.dy=this.dy3D,this.scrollbarY.dx=this.dx3D),this.fixColumns(),e.prototype.fixLayout.call(this)},t.prototype.fixColumns=function(){var e=this,t=1,i=0;k.each(this.series.iterator(),function(e){e instanceof Ge&&(!e.clustered&&i>0&&t++,e.depthIndex=t-1,i++)});var n=0;k.each(this.series.iterator(),function(i){if(i instanceof Ge){i.depth=e.depth/t,i.angle=e.angle,i.columnsContainer==e.columnsContainer&&(i.dx=e.depth/t*u.cos(e.angle)*i.depthIndex,i.dy=-e.depth/t*u.sin(e.angle)*i.depthIndex);var r=!1;(i.baseAxis==i.xAxis&&i.xAxis.renderer.inversed||i.baseAxis==i.yAxis&&i.yAxis.renderer.inversed)&&(r=!0);var a=1;i.dataItems.each(function(e){var t=e.column;t&&(t.zIndex=r?1e3*(1e3-a)+n-100*i.depthIndex:1e3*a+n-100*i.depthIndex,a++)}),r?n--:n++}}),this.maskColumns()},t.prototype.processConfig=function(t){if(t&&m.hasValue(t.series)&&m.isArray(t.series))for(var i=0,n=t.series.length;i<n;i++)t.series[i].type=t.series[i].type||"ColumnSeries3D";e.prototype.processConfig.call(this,t)},t.prototype.maskColumns=function(){var e=this.plotContainer.pixelWidth,t=this.plotContainer.pixelHeight,i=this.dx3D,n=this.dy3D,r=h.moveTo({x:0,y:0})+h.lineTo({x:i,y:n})+h.lineTo({x:e+i,y:n})+h.lineTo({x:e+i,y:t+n})+h.lineTo({x:e,y:t})+h.lineTo({x:e,y:t})+h.lineTo({x:0,y:t})+h.closePath(),a=this.columnsContainer;a&&a.mask&&(a.mask.path=r)},t}(a.a);l.b.registeredClasses.XYChart3D=Ue;var Ke=i("2OXf"),Ze=i("aM7D"),Qe=i("Uf57"),Je=i("YOID"),$e=function(e){function t(){var t=e.call(this)||this;return t.className="Candlestick",t.layout="none",t}return r.c(t,e),t.prototype.createAssets=function(){e.prototype.createAssets.call(this),this.lowLine=this.createChild(he.a),this.lowLine.shouldClone=!1,this.highLine=this.createChild(he.a),this.highLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.lowLine&&this.lowLine.copyFrom(t.lowLine),this.highLine&&this.highLine.copyFrom(t.highLine)},t}(He.a);l.b.registeredClasses.Candlestick=$e;var et=function(e){function t(){var t=e.call(this)||this;return t.values.lowValueX={},t.values.lowValueY={},t.values.highValueX={},t.values.highValueY={},t.className="CandlestickSeriesDataItem",t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"lowValueX",{get:function(){return this.values.lowValueX.value},set:function(e){this.setValue("lowValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"lowValueY",{get:function(){return this.values.lowValueY.value},set:function(e){this.setValue("lowValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueX",{get:function(){return this.values.highValueX.value},set:function(e){this.setValue("highValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueY",{get:function(){return this.values.highValueY.value},set:function(e){this.setValue("highValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueX",{get:function(){return this.values.valueX.value},set:function(e){this.setValue("valueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueY",{get:function(){return this.values.valueY.value},set:function(e){this.setValue("valueY",e)},enumerable:!0,configurable:!0}),t}(Re.b),tt=function(e){function t(){var t=e.call(this)||this;t.className="CandlestickSeries",t.groupFields.lowValueX="low",t.groupFields.lowValueY="low",t.groupFields.highValueX="high",t.groupFields.highValueY="high",t.strokeOpacity=1;var i=new N.a,n=i.getFor("positive"),r=i.getFor("negative");return t.dropFromOpenState.properties.fill=r,t.dropFromOpenState.properties.stroke=r,t.riseFromOpenState.properties.fill=n,t.riseFromOpenState.properties.stroke=n,t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Candlestick Series"))},t.prototype.createDataItem=function(){return new et},t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),this.validateCandlestick(t)},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.lowLine,n=t.highLine;if(this.baseAxis==this.xAxis){var r=t.pixelWidth/2;i.x=r,n.x=r;var a=e.getWorkingValue(this.yOpenField),o=e.getWorkingValue(this.yField),s=this.yAxis.getY(e,this.yOpenField),l=this.yAxis.getY(e,this.yField),u=this.yAxis.getY(e,this.yLowField),h=this.yAxis.getY(e,this.yHighField),c=t.pixelY;i.y1=u-c,n.y1=h-c,a<o?(i.y2=s-c,n.y2=l-c):(i.y2=l-c,n.y2=s-c)}if(this.baseAxis==this.yAxis){var p=t.pixelHeight/2;i.y=p,n.y=p;var d=e.getWorkingValue(this.xOpenField),y=e.getWorkingValue(this.xField),g=this.xAxis.getX(e,this.xOpenField),f=this.xAxis.getX(e,this.xField),m=this.xAxis.getX(e,this.xLowField),v=this.xAxis.getX(e,this.xHighField),x=t.pixelX;i.x1=m-x,n.x1=v-x,d<y?(i.x2=g-x,n.x2=f-x):(i.x2=f-x,n.x2=g-x)}k.each(this.axisRanges.iterator(),function(t){var r=e.rangesColumns.getKey(t.uid);if(r){var a=r.lowLine;a.x=i.x,a.y=i.y,a.x1=i.x1,a.x2=i.x2,a.y1=i.y1,a.y2=i.y2;var o=r.highLine;o.x=n.x,o.y=n.y,o.x1=n.x1,o.x2=n.x2,o.y1=n.y1,o.y2=n.y2}})}},Object.defineProperty(t.prototype,"xLowField",{get:function(){return this._xLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yLowField",{get:function(){return this._yLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"xHighField",{get:function(){return this._xHighField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yHighField",{get:function(){return this._yHighField},enumerable:!0,configurable:!0}),t.prototype.defineFields=function(){e.prototype.defineFields.call(this);var t=this.xAxis,i=this.yAxis;if(t&&i){if(this.baseAxis==t){var n=C.capitalize(i.axisFieldName);this._yLowField="low"+n+"Y",this._yHighField="high"+n+"Y"}if(this.baseAxis==i){var r=C.capitalize(t.axisFieldName);this._xLowField="low"+r+"X",this._xHighField="high"+r+"X"}this.addValueField(t,this._xValueFields,this._xLowField),this.addValueField(t,this._xValueFields,this._xHighField),this.addValueField(i,this._yValueFields,this._yLowField),this.addValueField(i,this._yValueFields,this._yHighField)}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n,r,a=e.createChild($e);a.shouldClone=!1,a.copyFrom(this.columns.template);var o=a.lowLine,s=a.highLine;this.baseAxis==this.yAxis?(n=t/3,r=i,o.y=i/2,s.y=i/2,o.x2=t/3,s.x2=t/3,s.x=t/3*2,a.column.x=t/3):(n=t,r=i/3,o.x=t/2,s.x=t/2,o.y2=i/3,s.y2=i/3,s.y=i/3*2,a.column.y=i/3),a.width=n,a.height=r,ne.copyProperties(this,e,J.b),ne.copyProperties(this.columns.template,a,J.b),a.stroke=this.riseFromOpenState.properties.stroke,a.fill=a.stroke;var l=e.dataItem;l.color=a.fill,l.colorOrig=a.fill},t.prototype.createColumnTemplate=function(){return new $e},t}(Re.a);l.b.registeredClasses.CandlestickSeries=tt,l.b.registeredClasses.CandlestickSeriesDataItem=et;var it=function(e){function t(){var t=e.call(this)||this;return t.className="OHLC",t.layout="none",t}return r.c(t,e),t.prototype.createAssets=function(){this.openLine=this.createChild(he.a),this.openLine.shouldClone=!1,this.highLowLine=this.createChild(he.a),this.highLowLine.shouldClone=!1,this.closeLine=this.createChild(he.a),this.closeLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.openLine&&this.openLine.copyFrom(t.openLine),this.highLowLine&&this.highLowLine.copyFrom(t.highLowLine),this.closeLine&&this.closeLine.copyFrom(t.closeLine)},t}($e);l.b.registeredClasses.OHLC=it;var nt=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(et),rt=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeries",t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("OHLC Series"))},t.prototype.createDataItem=function(){return new nt},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.openLine,n=t.highLowLine,r=t.closeLine;if(this.baseAxis==this.xAxis){var a=t.pixelWidth/2;n.x=a,e.getWorkingValue(this.yOpenField),e.getWorkingValue(this.yField);var o=this.yAxis.getY(e,this.yOpenField),s=this.yAxis.getY(e,this.yField),l=this.yAxis.getY(e,this.yLowField),u=this.yAxis.getY(e,this.yHighField),h=t.pixelY;i.y1=o-h,i.y2=o-h,i.x1=0,i.x2=a,r.y1=s-h,r.y2=s-h,r.x1=a,r.x2=2*a,n.y1=u-h,n.y2=l-h}if(this.baseAxis==this.yAxis){var c=t.pixelHeight/2;n.y=c,e.getWorkingValue(this.xOpenField),e.getWorkingValue(this.xField);var p=this.xAxis.getX(e,this.xOpenField),d=this.xAxis.getX(e,this.xField),y=this.xAxis.getX(e,this.xLowField),g=this.xAxis.getX(e,this.xHighField),f=t.pixelX;i.x1=p-f,i.x2=p-f,i.y1=c,i.y2=2*c,r.x1=d-f,r.x2=d-f,r.y1=0,r.y2=c,n.x1=g-f,n.x2=y-f}k.each(this.axisRanges.iterator(),function(t){var a=e.rangesColumns.getKey(t.uid);if(a){var o=a.openLine;o.x=i.x,o.y=i.y,o.x1=i.x1,o.x2=i.x2,o.y1=i.y1,o.y2=i.y2;var s=a.closeLine;s.x=r.x,s.y=r.y,s.x1=r.x1,s.x2=r.x2,s.y1=r.y1,s.y2=r.y2;var l=a.highLowLine;l.x=n.x,l.y=n.y,l.x1=n.x1,l.x2=n.x2,l.y1=n.y1,l.y2=n.y2}})}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var n,r,a=e.createChild(it);a.shouldClone=!1,a.copyFrom(this.columns.template);var o=a.openLine,s=a.closeLine,l=a.highLowLine;this.baseAxis==this.yAxis?(n=t/3,r=i,l.y=i/2,l.x2=t,o.x=t/3*2,o.y2=i/2,s.x=t/3,s.y2=i,s.y1=i/2):(n=t,r=i/3,l.x=t/2,l.y2=i,o.y=i/3*2,o.x2=t/2,s.y=i/3,s.x2=t,s.x1=t/2),a.width=n,a.height=r,ne.copyProperties(this,e,J.b),ne.copyProperties(this.columns.template,a,J.b),a.stroke=this.riseFromOpenState.properties.stroke;var u=e.dataItem;u.color=a.stroke,u.colorOrig=a.stroke},t.prototype.createColumnTemplate=function(){return new it},t}(tt);l.b.registeredClasses.OHLCSeries=rt,l.b.registeredClasses.OHLCSeriesDataItem=nt;var at=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesSegment",t}return r.c(t,e),t.prototype.drawSegment=function(e,t,i,n,r,a){if(e.length>0&&t.length>0)if(r){var o=h.moveTo(e[0]);if(e.length>0)for(var s=1;s<e.length;s++){var l=e[s];s/2==Math.round(s/2)?o+=h.moveTo(l):o+=h.lineTo(l)}this.strokeSprite.path=o,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(o=h.moveTo(e[0])+h.polyline(e),o+=h.lineTo(t[0])+h.polyline(t),o+=h.lineTo(e[0]),o+=h.closePath(),this.fillSprite.path=o)}else{o=h.moveTo(e[0])+h.polyline(e);this.strokeSprite.path=o,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(o+=h.lineTo(t[0])+h.polyline(t),o+=h.lineTo(e[0]),o+=h.closePath(),this.fillSprite.path=o)}},t}(Je.a);l.b.registeredClasses.StepLineSeriesSegment=at;var ot=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(s.b),st=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeries",t.applyTheme(),t.startLocation=0,t.endLocation=1,t}return r.c(t,e),t.prototype.createDataItem=function(){return new ot},t.prototype.addPoints=function(e,t,i,n,r){var a,o,s,l;this.baseAxis==this.xAxis&&(a=this.startLocation,o=this.endLocation,s=this.getAdjustedXLocation(t,this.yOpenField),l=this.getAdjustedXLocation(t,this.yField)),this.baseAxis==this.yAxis&&(s=this.startLocation,l=this.endLocation,a=this.getAdjustedXLocation(t,this.xOpenField),o=this.getAdjustedXLocation(t,this.xField));var h=this.xAxis.getX(t,i,a),c=this.yAxis.getY(t,n,s),p=this.xAxis.getX(t,i,o),d=this.yAxis.getY(t,n,l);if(h=u.fitToRange(h,-1e5,1e5),c=u.fitToRange(c,-1e5,1e5),p=u.fitToRange(p,-1e5,1e5),d=u.fitToRange(d,-1e5,1e5),!this.noRisers&&e.length>1){var y=e[e.length-1];this.baseAxis==this.xAxis&&(r?e.push({x:y.x,y:d}):e.push({x:h,y:y.y})),this.baseAxis==this.yAxis&&(r?e.push({x:p,y:y.y}):e.push({x:y.x,y:c}))}var g={x:h,y:c},f={x:p,y:d};r?e.push(f,g):e.push(g,f)},t.prototype.drawSegment=function(e,t,i){var n=!1;this.yAxis==this.baseAxis&&(n=!0),e.drawSegment(t,i,this.tensionX,this.tensionY,this.noRisers,n)},t.prototype.createSegment=function(){return new at},Object.defineProperty(t.prototype,"noRisers",{get:function(){return this.getPropertyValue("noRisers")},set:function(e){this.setPropertyValue("noRisers",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(e){this.setPropertyValue("startLocation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(e){this.setPropertyValue("endLocation",e,!0)},enumerable:!0,configurable:!0}),t}(s.a);l.b.registeredClasses.StepLineSeries=st,l.b.registeredClasses.StepLineSeriesDataItem=ot;var lt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumn",t}return r.c(t,e),t.prototype.createAssets=function(){this.radarColumn=this.createChild(Ie.a),this.radarColumn.shouldClone=!1,this.radarColumn.strokeOpacity=void 0,this.column=this.radarColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.radarColumn&&this.radarColumn.copyFrom(t.radarColumn)},t.prototype.getTooltipX=function(){var e=this.getPropertyValue("tooltipX");return m.isNumber(e)?e:this.radarColumn.getTooltipX()},t.prototype.getTooltipY=function(){var e=this.getPropertyValue("tooltipX");return m.isNumber(e)?e:this.radarColumn.getTooltipY()},t}(He.a);l.b.registeredClasses.RadarColumn=lt;var ut=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(Re.b),ht=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumnSeries",t.applyTheme(),t}return r.c(t,e),t.prototype.createColumnTemplate=function(){return new lt},t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.disableUnusedColumns=function(e){e&&(e.column&&(e.column.__disabled=!0),k.each(this.axisRanges.iterator(),function(t){var i=e.rangesColumns.getKey(t.uid);i&&(i.__disabled=!0)}))},t.prototype.validateDataElementReal=function(e){var t,i,n,r,a=this,s=this.chart.startAngle,l=this.chart.endAngle,h=this.yField,c=this.yOpenField,p=this.xField,d=this.xOpenField,y=this.getStartLocation(e),g=this.getEndLocation(e),f=(l-s)/(this.dataItems.length*(this.end-this.start)),m=e.column;m||(m=this.columns.create(),e.column=m,ne.forceCopyProperties(this.columns.template,m,J.b),e.addSprite(m),m.paper=this.paper,this.setColumnStates(m));var v=m.width,x=100;v instanceof o.a&&(x=v.percent);var b=u.round((g-y)*(1-x/100)/2,5);if(y+=b,g-=b,this.baseAxis==this.xAxis?(n=u.getDistance({x:this.yAxis.getX(e,h,e.locations[h],"valueY"),y:this.yAxis.getY(e,h,e.locations[h],"valueY")}),r=u.getDistance({x:this.yAxis.getX(e,c,e.locations[c],"valueY"),y:this.yAxis.getY(e,c,e.locations[c],"valueY")}),t=this.xAxis.getAngle(e,d,y,"valueX"),i=this.xAxis.getAngle(e,p,g,"valueX"),s+=y*f,l-=(1-g)*f):(n=u.getDistance({x:this.yAxis.getX(e,h,y,"valueY"),y:this.yAxis.getY(e,h,y,"valueY")}),r=u.getDistance({x:this.yAxis.getX(e,c,g,"valueY"),y:this.yAxis.getY(e,c,g,"valueY")}),t=this.xAxis.getAngle(e,p,e.locations[p],"valueX"),i=this.xAxis.getAngle(e,d,e.locations[d],"valueX")),i<t){var P=i;i=t,t=P}t=u.fitToRange(t,s,l),i=u.fitToRange(i,s,l);var C=m.radarColumn;C.startAngle=t;var A=i-t;A>0?(C.arc=A,C.radius=n,C.innerRadius=r,m.__disabled=!1,m.parent=this.columnsContainer,k.each(this.axisRanges.iterator(),function(i){var o=e.rangesColumns.getKey(i.uid);o||(o=a.columns.create(),ne.forceCopyProperties(a.columns.template,o,J.b),ne.copyProperties(i.contents,o,J.b),o.dataItem&&je.remove(o.dataItem.sprites,o),e.addSprite(o),o.paper=a.paper,a.setColumnStates(o),e.rangesColumns.setKey(i.uid,o));var s=o.radarColumn;s.startAngle=t,s.arc=A,s.radius=n,s.innerRadius=r,s.invalid&&(s.paper=a.paper,s.validate()),o.__disabled=!1,o.parent=i.contents})):this.disableUnusedColumns(e)},t.prototype.getPoint=function(e,t,i,n,r,a,o){a||(a="valueX"),o||(o="valueY");var s=this.yAxis.getX(e,i,r,o),l=this.yAxis.getY(e,i,r,o),h=u.getDistance({x:s,y:l});0==h&&(h=1e-5);var c=this.xAxis.getAngle(e,t,n,a);return{x:h*u.cos(c),y:h*u.sin(c)}},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return h.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t.prototype.positionBulletReal=function(e,t,i){var n=this.xAxis,r=this.yAxis;(t<n.start||t>n.end||i<r.start||i>r.end)&&(e.visible=!1),e.moveTo(this.xAxis.renderer.positionToPoint(t,i))},t.prototype.setXAxis=function(t){e.prototype.setXAxis.call(this,t),this.updateRendererRefs()},t.prototype.setYAxis=function(t){e.prototype.setYAxis.call(this,t),this.updateRendererRefs()},t.prototype.updateRendererRefs=function(){var e=this.xAxis.renderer,t=this.yAxis.renderer;e.axisRendererY=t},t}(Re.a);l.b.registeredClasses.RadarColumnSeries=ht,l.b.registeredClasses.RadarColumnSeriesDataItem=ut;var ct=i("AC2I"),pt=function(e){function t(){var t=e.call(this)||this;return t.slice=t.createChild(J.a),t.slice.shouldClone=!1,t.slice.setElement(t.paper.add("path")),t.slice.isMeasured=!1,t.orientation="vertical",t.bottomWidth=Object(o.c)(100),t.topWidth=Object(o.c)(100),t.isMeasured=!1,t.width=10,t.height=10,t.expandDistance=0,t.className="FunnelSlice",t.applyTheme(),t}return r.c(t,e),t.prototype.draw=function(){e.prototype.draw.call(this);var t=this.pixelPaddingTop,i=this.pixelPaddingBottom,n=this.pixelPaddingRight,r=this.pixelPaddingLeft,a=this.pixelWidth-n-r,o=this.pixelHeight-t-i,s=this.expandDistance,l="";if("vertical"==this.orientation){var u={x:(a-(v=C.relativeToValue(this.topWidth,a)))/2+r,y:t},c={x:(a+v)/2+r,y:t},p={x:(a+(x=C.relativeToValue(this.bottomWidth,a)))/2+r,y:t+o},d={x:(a-x)/2+r,y:t+o},y={x:c.x+(p.x-c.x)/2+s*o,y:c.y+.5*o},g={x:u.x+(d.x-u.x)/2-s*o,y:u.y+.5*o},f=h.lineTo(p),m="";0!=s&&(f=h.quadraticCurveTo(p,y),m=h.quadraticCurveTo(u,g)),l=h.moveTo(u)+h.lineTo(c)+f+h.lineTo(d)+m,this.tickPoint={x:c.x+(p.x-c.x)/2,y:c.y+(p.y-c.y)/2}}else{var v,x,b={x:r,y:(o-(v=C.relativeToValue(this.topWidth,o)))/2+t},P={x:r,y:(o+v)/2+t},A={x:r+a,y:(o-(x=C.relativeToValue(this.bottomWidth,o)))/2+t},D={x:r+a,y:(o+x)/2+t};y={y:b.y+(A.y-b.y)/2-s*a,x:b.x+.5*a},g={y:P.y+(D.y-P.y)/2+s*a,x:P.x+.5*a},f=h.lineTo(A),m="";0!=s&&(f=h.quadraticCurveTo(A,y),m=h.quadraticCurveTo(P,g)),l=h.moveTo(P)+h.lineTo(b)+f+h.lineTo(D)+m,this.tickPoint={y:P.y+(D.y-P.y)/2,x:P.x+(D.x-P.x)/2}}this.slice.path=l,this.invalidateLayout()},t.prototype.getPoint=function(e,t){var i=this.pixelPaddingTop,n=this.pixelPaddingBottom,r=this.pixelPaddingRight,a=this.pixelPaddingLeft,o=this.pixelWidth-r-a,s=this.pixelHeight-i-n;if("vertical"==this.orientation){var l={x:(o-(p=C.relativeToValue(this.topWidth,o)))/2+a,y:i},u={x:(o+p)/2+a,y:i},h={x:(o+(d=C.relativeToValue(this.bottomWidth,o)))/2+a,y:i+s},c=l.x+({x:(o-d)/2+a,y:i+s}.x-l.x)*t;return{x:c+(u.x+(h.x-u.x)*t-c)*e,y:u.y+(h.y-u.y)*t}}var p,d,y={x:a,y:(s-(p=C.relativeToValue(this.topWidth,s)))/2+i},g={x:a,y:(s+p)/2+i},f={x:a+o,y:(s-(d=C.relativeToValue(this.bottomWidth,s)))/2+i},m=y.y+(f.y-y.y)*e;return{y:m+(g.y+({x:a+o,y:(s+d)/2+i}.y-g.y)*e-m)*t,x:y.x+(f.x-y.x)*e}},Object.defineProperty(t.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(e){this.setPercentProperty("bottomWidth",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(e){this.setPercentProperty("topWidth",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"expandDistance",{get:function(){return this.getPropertyValue("expandDistance")},set:function(e){this.setPropertyValue("expandDistance",e,!0)},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.slice&&this.slice.copyFrom(t.slice)},t}(d.a);l.b.registeredClasses.FunnelSlice=pt;var dt=i("qzbU"),yt=function(e){function t(){var t=e.call(this)||this;return t._label=new _.d,t._slice=new _.d,t.className="FunnelTick",t.element=t.paper.add("path"),t._disposers.push(t._label),t._disposers.push(t._slice),t.setPropertyValue("locationX",0),t.setPropertyValue("locationY",0),t.applyTheme(),t}return r.c(t,e),t.prototype.draw=function(){e.prototype.draw.call(this);var t=this.slice,i=t.getPoint(this.locationX,this.locationY);if(i){var n=this.label,r=t.dataItem.component;if("vertical"==r.orientation){var a=n.pixelX,o=n.pixelY;r.labelsOpposite||(a+=n.maxRight);var s=C.spritePointToSprite(i,t,this.parent),l=C.spritePointToSprite({x:a,y:o},n.parent,this.parent);this.path=h.moveTo(s)+h.lineTo(l)}else{a=n.pixelX,o=n.pixelY;r.labelsOpposite||(o+=n.maxBottom);s=C.spritePointToSprite(i,t,this.parent),l=C.spritePointToSprite({x:a,y:o},n.parent,this.parent);this.path=h.moveTo(s)+h.lineTo(l)}}},Object.defineProperty(t.prototype,"slice",{get:function(){return this._slice.get()},set:function(e){this._slice.set(e,new _.c([e.events.on("transformed",this.invalidate,this,!1),e.events.on("validated",this.invalidate,this,!1)]))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this._label.get()},set:function(e){this._label.set(e,e.events.on("transformed",this.invalidate,this,!1))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"locationX",{get:function(){return this.getPropertyValue("locationX")},set:function(e){this.setPropertyValue("locationX",e,!1,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"locationY",{get:function(){return this.getPropertyValue("locationY")},set:function(e){this.setPropertyValue("locationY",e,!1,!0)},enumerable:!0,configurable:!0}),t}(dt.a);l.b.registeredClasses.FunnelTick=yt;var gt=function(e){function t(){var t=e.call(this)||this;return t.className="FunnelSeriesDataItem",t.events.on("visibilitychanged",function(){t.component&&t.component.invalidateDataItems()},t,!1),t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"sliceLink",{get:function(){var e=this;if(!this._sliceLink){var t=this.component.sliceLinks.create();this._sliceLink=t,this._disposers.push(t),t.parent=this.component.slicesContainer,this._disposers.push(new _.b(function(){e.component&&e.component.sliceLinks.removeValue(t)})),this.addSprite(t),t.visible=this.visible}return this._sliceLink},enumerable:!0,configurable:!0}),t}(ct.b),ft=function(e){function t(){var t=e.call(this)||this;return t._nextY=0,t.className="FunnelSeries",t.orientation="vertical",t.width=Object(o.c)(100),t.height=Object(o.c)(100),t.slicesContainer.width=Object(o.c)(100),t.slicesContainer.height=Object(o.c)(100),t._disposers.push(t.slicesContainer.events.on("maxsizechanged",t.invalidateDataItems,t,!1)),t.labelsOpposite=!0,t.labelsContainer.layout="absolute",t.bottomRatio=0,t.applyTheme(),t}return r.c(t,e),t.prototype.createSlice=function(){return new pt},t.prototype.createTick=function(){return new yt},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Funnel Series"))},t.prototype.createDataItem=function(){return new gt},t.prototype.initSlice=function(e){e.isMeasured=!1,e.defaultState.properties.scale=1,e.observe("scale",this.handleSliceScale,this),e.observe(["dx","dy","x","y"],this.handleSliceMove,this),e.tooltipText="{category}: {value.percent.formatNumber('#.#')}% ({value.value})",e.states.create("hover").properties.expandDistance=.2},t.prototype.initLabel=function(t){e.prototype.initLabel.call(this,t),t.verticalCenter="middle",t.horizontalCenter="middle",t.isMeasured=!0,t.padding(5,5,5,5)},t.prototype.validate=function(){e.prototype.validate.call(this),this._nextY=0},t.prototype.validateDataElements=function(){var t=this,i=this.slicesContainer,n=this.labelsContainer,r=this.labels.template;this.alignLabels?(r.interactionsEnabled=!0,i.isMeasured=!0,n.isMeasured=!0):(r.interactionsEnabled=!1,i.isMeasured=!1,n.isMeasured=!1);var a=0,o=0;this.dataItems.each(function(e){m.hasValue(e.value)&&(o++,e.value>0?a+=e.getWorkingValue("value")/e.value:t.ignoreZeroValues?o--:!e.visible||e.__disabled||e.isHiding?o--:a+=1)}),this._total=1/o*a,this._count=o,e.prototype.validateDataElements.call(this),this.arrangeLabels()},t.prototype.getNextValue=function(e){var t=e.index,i=e.getWorkingValue("value");if(t<this.dataItems.length-1){var n=this.dataItems.getIndex(t+1);if(i=n.getWorkingValue("value"),!n.visible||n.isHiding||n.__disabled||0==n.value&&this.ignoreZeroValues)return this.getNextValue(n)}return i},t.prototype.formDataElement=function(){},t.prototype.validateDataElement=function(t){if(m.hasValue(t.value)){var i=t.slice;i.orientation=this.orientation;var n=t.sliceLink;n.orientation=this.orientation;var r=t.tick,a=t.label;r.slice=i,r.label=a,this.decorateSlice(t),n.fill=i.fill,t.index==this.dataItems.length-1&&(n.disabled=!0),e.prototype.validateDataElement.call(this,t)}},t.prototype.decorateSlice=function(e){var t=e.slice,i=e.sliceLink,n=e.label,r=e.tick,a=this.slicesContainer.innerWidth,o=this.slicesContainer.innerHeight,s=this.getNextValue(e),l=Math.abs(e.getWorkingValue("value")),h=this.bottomRatio,c=1;if(0!=e.value?c=l/Math.abs(e.value):(e.__disabled||e.isHiding||!e.visible)&&(c=1e-6),this.ignoreZeroValues&&0==e.value)e.__disabled=!0;else if(e.__disabled=!1,this._nextY==1/0&&(this._nextY=0),"vertical"==this.orientation){var p=i.pixelHeight*c;o+=p,t.topWidth=l/this.dataItem.values.value.high*a,t.bottomWidth=(l-(l-s)*h)/this.dataItem.values.value.high*a,i.topWidth=t.bottomWidth,i.bottomWidth=(l-(l-s))/this.dataItem.values.value.high*a,t.y=this._nextY,t.height=Math.min(1e5,u.max(0,o/this._count*c/this._total-p)),t.x=a/2,this.alignLabels?n.x=void 0:n.x=t.x,n.y=t.pixelY+t.pixelHeight*r.locationY,this._nextY+=t.pixelHeight+p,i.y=this._nextY-p,i.x=t.x}else{var d=i.pixelWidth*c;a+=d,t.topWidth=l/this.dataItem.values.value.high*o,t.bottomWidth=(l-(l-s)*h)/this.dataItem.values.value.high*o,i.topWidth=t.bottomWidth,i.bottomWidth=(l-(l-s))/this.dataItem.values.value.high*o,t.x=this._nextY,t.width=Math.min(1e5,a/this._count*c*1/this._total-d),t.y=o/2,this.alignLabels?n.y=this.labelsContainer.measuredHeight:n.y=t.y,n.x=t.pixelX+t.pixelWidth*r.locationX,this._nextY+=t.pixelWidth+d,i.x=this._nextY-d,i.y=t.y}},t.prototype.getLastLabel=function(e){if(e>0){var t=this.labels.getIndex(e);return t.__disabled||!t.visible?this.getLastLabel(e-1):t}},t.prototype.arrangeLabels=function(){if(this.alignLabels){var e=this.labels.length;if(e>1){var t=this.getLastLabel(e-1);if(t){var i=t.pixelY,n=t.pixelX;if(e>1){for(var r=e-2;r>=0;r--){(a=this.labels.getIndex(r)).visible&&!a.__disabled&&(a.invalid&&a.validate(),"vertical"==this.orientation?a.pixelY+a.measuredHeight>i&&(a.y=Math.min(1e6,i-a.measuredHeight)):a.pixelX+a.measuredWidth>n&&(a.x=Math.min(1e6,n-a.measuredWidth)),i=a.pixelY,n=a.pixelX)}i=0,n=0;for(r=0;r<e;r++){var a;(a=this.labels.getIndex(r)).visible&&!a.__disabled&&(a.invalid&&a.validate(),"vertical"==this.orientation?a.pixelY<i&&(a.y=Math.min(1e6,i)):a.pixelX<n&&(a.x=Math.min(1e6,n)),i+=a.measuredHeight,n+=a.measuredWidth)}}}}}},t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem.slice,n=t.locationX;m.isNumber(n)||(n=.5);var r=t.locationY;m.isNumber(r)||(r=1),t.x=i.pixelX+i.measuredWidth*n,t.y=i.pixelY+i.measuredHeight*r},Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e)&&(this.labelsOpposite=this.labelsOpposite,this.invalidate(),"vertical"==e?(this.ticks.template.locationX=1,this.ticks.template.locationY=.5,this.labels.template.rotation=0,this.layout="horizontal"):(this.ticks.template.locationX=.5,this.ticks.template.locationY=1,this.labels.template.rotation=-90,this.layout="vertical"))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomRatio",{get:function(){return this.getPropertyValue("bottomRatio")},set:function(e){this.setPropertyValue("bottomRatio",e)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sliceLinks",{get:function(){if(!this._sliceLinks){var e=new pt;e.applyOnClones=!0,e.fillOpacity=.5,e.expandDistance=-.3,e.hiddenState.properties.opacity=0,this._disposers.push(e),this._sliceLinks=new S.e(e),this._disposers.push(new S.c(this._sliceLinks))}return this._sliceLinks},enumerable:!0,configurable:!0}),t.prototype.show=function(t){var i=this,n=this.startIndex,r=this.endIndex,a=this.defaultState.transitionDuration;m.isNumber(t)&&(a=t);var o=0;return k.each(k.indexed(this.dataItems.iterator()),function(e){var t=e[0],s=e[1];i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*t+a*(t-n)/(r-n)),s.show(a,o,["value"])}),e.prototype.show.call(this,t)},t.prototype.hide=function(t){var i=this,n=["value"],r=this.startIndex,a=this.endIndex,o=0,s=this.hiddenState.transitionDuration;m.isNumber(t)&&(s=t),k.each(k.indexed(this.dataItems.iterator()),function(e){var t=e[0],l=e[1];i.sequencedInterpolation&&(o=i.sequencedInterpolationDelay*t+s*(t-r)/(a-r)),l.hide(s,o,0,n)});var l=e.prototype.hide.call(this,t);return l&&!l.isFinished()&&l.delay(o),l},t.prototype.setAlignLabels=function(t){e.prototype.setAlignLabels.call(this,t),this.ticks.template.disabled=!t;var i=this.labelsContainer;i&&(t?(i.height=void 0,i.width=void 0,i.margin(10,10,10,10)):(i.width=Object(o.c)(100),i.height=Object(o.c)(100))),this.labelsOpposite=this.labelsOpposite},Object.defineProperty(t.prototype,"labelsOpposite",{get:function(){return this.getPropertyValue("labelsOpposite")},set:function(e){this.setPropertyValue("labelsOpposite",e);var t=this.labels.template,i="none",n="none";this.alignLabels?e?(this.labelsContainer.toFront(),"vertical"==this.orientation?(this.ticks.template.locationX=1,t.horizontalCenter="left",i="right"):(this.ticks.template.locationY=1,t.horizontalCenter="right",n="bottom")):(this.labelsContainer.toBack(),"vertical"==this.orientation?(this.ticks.template.locationX=0,i="left"):(n="top",this.ticks.template.locationY=0)):"vertical"==this.orientation?i="center":n="middle",t.align=i,t.valign=n,this.validateLayout(),this.ticks.each(function(e){e.invalidate()}),this.invalidateDataItems()},enumerable:!0,configurable:!0}),t}(ct.a);l.b.registeredClasses.FunnelSeries=ft,l.b.registeredClasses.FunnelSeriesDataItem=gt;var mt=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(gt),vt=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeries",t.topWidth=Object(o.c)(0),t.bottomWidth=Object(o.c)(100),t.pyramidHeight=Object(o.c)(100),t.valueIs="area",t.sliceLinks.template.width=0,t.sliceLinks.template.height=0,t.applyTheme(),t}return r.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new mt},t.prototype.validate=function(){e.prototype.validate.call(this),this._nextWidth=void 0},t.prototype.getNextValue=function(e){var t=e.index,i=e.getWorkingValue("value");t<this.dataItems.length-1&&(i=this.dataItems.getIndex(t+1).getWorkingValue("value"));return 0==i&&(i=1e-6),i},t.prototype.validateDataElements=function(){var t=this,i=this.slicesContainer.innerWidth,n=this.slicesContainer.innerHeight;if(this.dataItems.each(function(e){if(e.value>0){var r=e.getWorkingValue("value")/e.value,a=e.sliceLink;"vertical"==t.orientation?n-=a.pixelHeight*r:i-=a.pixelWidth*r}}),this._pyramidHeight=C.relativeToValue(this.pyramidHeight,n),this._pyramidWidth=C.relativeToValue(this.pyramidHeight,i),"vertical"==this.orientation){var r=(n-this._pyramidHeight)/2;this.slicesContainer.y=r,this.labelsContainer.y=r,this.ticksContainer.y=r}else{var a=(i-this._pyramidWidth)/2;this.slicesContainer.x=a,this.labelsContainer.x=a,this.ticksContainer.x=a}e.prototype.validateDataElements.call(this)},t.prototype.decorateSlice=function(e){var t=this.dataItem.values.value.sum;if(0!=t){var i=e.slice,n=e.sliceLink,r=e.label,a=e.tick;this.getNextValue(e);var o=Math.abs(e.getWorkingValue("value"));0==o&&(o=1e-6);var s=this._pyramidWidth,l=this._pyramidHeight,u=this.slicesContainer.innerWidth,h=this.slicesContainer.innerHeight,c=n.pixelWidth,p=n.pixelHeight;if(0==e.value&&this.ignoreZeroValues?e.__disabled=!0:e.__disabled=!1,"vertical"==this.orientation){var d=C.relativeToValue(this.topWidth,u);m.isNumber(this._nextWidth)||(this._nextWidth=d);var y=C.relativeToValue(this.bottomWidth,u),g=this._nextWidth,f=Math.atan2(l,d-y);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var v=void 0,x=void 0;if("area"==this.valueIs){var b=(d+y)/2*l*o/t,P=Math.abs(g*g-2*b*A);x=(2*b-(v=(g-Math.sqrt(P))/A)*g)/v}else{x=g-(v=l*o/this.dataItem.values.value.sum)*A}i.height=v,i.width=u,i.bottomWidth=x,i.topWidth=g,n.topWidth=i.bottomWidth,n.bottomWidth=i.bottomWidth,i.y=this._nextY,this.alignLabels?r.x=0:r.x=u/2,r.y=i.pixelY+i.pixelHeight*a.locationY+i.dy,this._nextY+=i.pixelHeight+p*o/Math.max(Math.abs(e.value),1e-8),n.y=this._nextY-p,n.x=u/2}else{d=C.relativeToValue(this.topWidth,h);m.isNumber(this._nextWidth)||(this._nextWidth=d);var A;y=C.relativeToValue(this.bottomWidth,h),g=this._nextWidth,f=Math.atan2(s,d-y);0==(A=Math.tan(Math.PI/2-f))&&(A=1e-8);var D=void 0;x=void 0;if("area"==this.valueIs)x=(2*(b=(d+y)/2*s*o/this.dataItem.values.value.sum)-(D=(g-Math.sqrt(g*g-2*b*A))/A)*g)/D;else x=g-(D=s*o/this.dataItem.values.value.sum)*A;i.width=D,i.height=h,i.bottomWidth=x,i.topWidth=g,n.topWidth=i.bottomWidth,n.bottomWidth=i.bottomWidth,i.x=this._nextY,this.alignLabels?r.y=this.labelsContainer.measuredHeight:r.y=h/2,r.x=i.pixelX+i.pixelWidth*a.locationX+i.dx,this._nextY+=i.pixelWidth+c*o/Math.max(Math.abs(e.value),1e-8),n.x=this._nextY-c,n.y=h/2}this._nextWidth=i.bottomWidth}},Object.defineProperty(t.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(e){this.setPercentProperty("topWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pyramidHeight",{get:function(){return this.getPropertyValue("pyramidHeight")},set:function(e){this.setPercentProperty("pyramidHeight",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(e){this.setPercentProperty("bottomWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"valueIs",{get:function(){return this.getPropertyValue("valueIs")},set:function(e){this.setPropertyValue("valueIs",e)&&this.invalidate()},enumerable:!0,configurable:!0}),t}(ft);l.b.registeredClasses.PyramidSeries=vt,l.b.registeredClasses.PyramidSeriesDataItem=mt;var xt=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(mt),bt=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeries",t.topWidth=Object(o.c)(100),t.bottomWidth=Object(o.c)(100),t.valueIs="height",t.applyTheme(),t.startLocation=0,t.endLocation=1,t.align="center",t.valign="middle",t._maskSprite=t.slicesContainer.createChild(J.a),t._maskSprite.visible=!1,t._maskSprite.zIndex=100,t._maskSprite.shouldClone=!1,t}return r.c(t,e),t.prototype.validateDataElements=function(){var t=this.slicesContainer.maxWidth,i=this.slicesContainer.maxHeight,n=this._maskSprite,r=n.measuredWidth/n.scale,a=n.measuredHeight/n.scale,o=u.min(i/a,t/r);o==1/0&&(o=1),o=u.max(.001,o);var s,l,h=this.startLocation,c=this.endLocation,p=u.min(t,r*o),d=u.min(i,a*o);n.scale=o,"vertical"==this.orientation?(this.topWidth=p+4,this.bottomWidth=p+4,this.pyramidHeight=d*(c-h),n.x=t/2,n.y=d/2):(this.topWidth=d+4,this.bottomWidth=d+4,this.pyramidHeight=p*(c-h),n.valign="middle",n.x=p/2,n.y=i/2),n.verticalCenter="middle",n.horizontalCenter="middle",e.prototype.validateDataElements.call(this),"vertical"==this.orientation?("bottom"==this.valign&&(s=i-d),"middle"==this.valign&&(s=(i-d)/2),"top"==this.valign&&(s=0),"left"==this.align&&(l=-(t-p)/2),"center"==this.align&&(l=0),"right"==this.align&&(l=(t-p)/2),this.slices.template.dy=h*d,this.alignLabels&&(this.slicesContainer.dx=l)):("bottom"==this.valign&&(s=(i-d)/2),"middle"==this.valign&&(s=0),"top"==this.valign&&(s=-(i-d)/2),"left"==this.align&&(l=0),"center"==this.align&&(l=(t-p)/2),"right"==this.align&&(l=t-p),this.slices.template.dx=h*p,this.alignLabels&&(this.slicesContainer.dy=s)),this.slicesContainer.x=l,this.labelsContainer.x=l,this.ticksContainer.x=l,this.slicesContainer.y=s,this.labelsContainer.y=s,this.ticksContainer.y=s,p>0&&d>0&&(this.slicesContainer.mask=n)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),m.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new xt},Object.defineProperty(t.prototype,"maskSprite",{get:function(){return this._maskSprite},enumerable:!0,configurable:!0}),t.prototype.initSlice=function(t){e.prototype.initSlice.call(this,t);var i=t.states.getKey("hover");i&&(i.properties.expandDistance=0)},Object.defineProperty(t.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(e){this.setPropertyValue("startLocation",e)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(e){this.setPropertyValue("endLocation",e)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),t}(vt);l.b.registeredClasses.PictorialStackedSeries=bt,l.b.registeredClasses.PictorialStackedSeriesDataItem=xt;var Pt=i("BmDP"),Ct=i("ncT3"),At=function(e){function t(){var t=e.call(this)||this;return t.className="ConeColumn",t}return r.c(t,e),t.prototype.createAssets=function(){this.coneColumn=this.createChild(Ct.a),this.coneColumn.shouldClone=!1,this.column=this.coneColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.coneColumn&&this.coneColumn.copyFrom(t.coneColumn)},t}(He.a);l.b.registeredClasses.ConeColumn=At;var Dt=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(Re.b),It=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeries",t.applyTheme(),t}return r.c(t,e),t.prototype.createColumnTemplate=function(){return new At},t.prototype.getMaskPath=function(){var e=0,t=0,i=this.columns.getIndex(0);if(i)return this.baseAxis==this.xAxis?t=i.coneColumn.bottom.radiusY+1:e=i.coneColumn.bottom.radiusY+1,h.rectToPath({x:-e,y:0,width:this.xAxis.axisLength+e,height:this.yAxis.axisLength+t})},t.prototype.validateDataElementReal=function(t){if(e.prototype.validateDataElementReal.call(this,t),t.column){var i=t.column.coneColumn;i.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical"}},t}(Re.a);l.b.registeredClasses.ConeSeries=It,l.b.registeredClasses.ConeSeriesDataItem=Dt;var Tt=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumn",t}return r.c(t,e),t.prototype.createAssets=function(){this.curvedColumn=this.createChild(J.a),this.curvedColumn.shouldClone=!1,this.setPropertyValue("tension",.7),this.width=Object(o.c)(120),this.height=Object(o.c)(120),this.column=this.curvedColumn},t.prototype.draw=function(){e.prototype.draw.call(this);var t,i=this.realWidth,n=this.realHeight,r=this.realX-this.pixelX,a=this.realY-this.pixelY;C.used(this.width);var o=1,s=1;"vertical"==this.orientation?(o=this.tension,t=[{x:0,y:n+a},{x:i/2,y:a},{x:i,y:n+a}]):(s=this.tension,t=[{x:r,y:0},{x:r+i,y:n/2},{x:r,y:n}]);var l=h.moveTo(t[0])+new be.b(o,s).smooth(t);this.column.path=l},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.curvedColumn&&this.curvedColumn.copyFrom(t.curvedColumn)},Object.defineProperty(t.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(e){this.setPropertyValue("tension",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0)},enumerable:!0,configurable:!0}),t}(He.a);l.b.registeredClasses.CurvedColumn=Tt;var Vt=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeriesDataItem",t.applyTheme(),t}return r.c(t,e),t}(Re.b),_t=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeries",t.applyTheme(),t}return r.c(t,e),t.prototype.createColumnTemplate=function(){return new Tt},t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t);var i=t.column;(i=t.column)&&(t.column.curvedColumn.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical")},t}(Re.a);l.b.registeredClasses.CurvedColumnSeries=_t,l.b.registeredClasses.CurvedColumnSeriesDataItem=Vt;var Lt=i("AAkI"),kt=i("eN1s"),Ot=i("TDx+"),Rt=i("eAid"),St=i("Uslz"),wt=i("+K/x"),Nt=i("KknQ"),jt=function(e){function t(){var t=e.call(this)||this;return t.className="DurationAxisDataItem",t.applyTheme(),t}return r.c(t,e),t}(Oe.b),Ft=function(e){function t(){var t=e.call(this)||this;return t._baseUnit="second",t.className="DurationAxis",t.setPropertyValue("maxZoomFactor",1e6),t.applyTheme(),t}return r.c(t,e),t.prototype.formatLabel=function(e,t){return this.durationFormatter.format(e,t||this.axisDurationFormat)},t.prototype.adjustMinMax=function(t,i,n,a,o){var s,l,h,c=this.baseUnit;if(this.setPropertyValue("maxPrecision",0),"millisecond"==c||"second"==c||"minute"==c||"hour"==c){a<=1&&(a=1),a=Math.round(a);var p=t,d=i;0===n&&(n=Math.abs(i));var y,g=[60,30,20,15,10,2,1],f=1;"hour"==c&&(g=[24,12,6,4,2,1]);try{for(var m=r.g(g),v=m.next();!v.done;v=m.next()){var x=v.value;if(n/x>a){f=x;break}}}catch(e){l={error:e}}finally{try{v&&!v.done&&(h=m.return)&&h.call(m)}finally{if(l)throw l.error}}var b=Math.ceil((i-t)/f/a),P=Math.log(Math.abs(b))*Math.LOG10E,C=Math.pow(10,Math.floor(P))/10,A=b/C;y=f*(b=u.closest(g,A)*C),this.durationFormatter.getValueUnit(y,this.baseUnit),t=Math.floor(t/y)*y,i=Math.ceil(i/y)*y,o&&((t-=y)<0&&p>=0&&(t=0),(i+=y)>0&&d<=0&&(i=0)),s={min:t,max:i,step:y}}else s=e.prototype.adjustMinMax.call(this,t,i,n,a,o);return this.axisDurationFormat=this.durationFormatter.getFormat(s.step,s.max,this.baseUnit),s},Object.defineProperty(t.prototype,"tooltipDurationFormat",{get:function(){return this._tooltipDurationFormat},set:function(e){this._tooltipDurationFormat=e},enumerable:!0,configurable:!0}),t.prototype.getTooltipText=function(e){var t=u.round(this.positionToValue(e),this._stepDecimalPlaces),i=this.formatLabel(t,this.tooltipDurationFormat);return this._adapterO?this._adapterO.apply("getTooltipText",i):i},Object.defineProperty(t.prototype,"baseUnit",{get:function(){return this._baseUnit},set:function(e){this._baseUnit!=e&&(this._baseUnit=e,this.durationFormatter.baseUnit=e,this.invalidate())},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.baseUnit=t.baseUnit},t}(Oe.a);l.b.registeredClasses.DurationAxis=Ft,l.b.registeredClasses.DurationAxisDataItem=jt;var Yt=i("9ZsQ"),Mt=i("ZoDA"),Wt=function(e){function t(){var t=e.call(this)||this;t.className="CircleBullet";var i=t.createChild(y.a);return i.shouldClone=!1,i.radius=5,i.isMeasured=!1,t.circle=i,t.applyTheme(),t}return r.c(t,e),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.circle.copyFrom(t.circle)},t}(se.a);l.b.registeredClasses.CircleBullet=Wt;var Xt=function(e){function t(){var t=e.call(this)||this;return t.className="ErrorBullet",t.errorLine=t.createChild(J.a),t.errorLine.shouldClone=!1,t.width=20,t.height=20,t.strokeOpacity=1,t.isDynamic=!0,t}return r.c(t,e),t.prototype.validatePosition=function(){e.prototype.validatePosition.call(this);var t=this.pixelWidth/2,i=this.pixelHeight/2;this.errorLine.path=h.moveTo({x:-t,y:-i})+h.lineTo({x:t,y:-i})+h.moveTo({x:0,y:-i})+h.lineTo({x:0,y:i})+h.moveTo({x:-t,y:i})+h.lineTo({x:t,y:i})},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.errorLine.copyFrom(t.errorLine)},t}(se.a);l.b.registeredClasses.ErrorBullet=Xt;var Ht=i("C6Lh"),Bt=i("Y9w3"),zt=i("A6AV"),Et=i("Trvg"),Gt=i("Rnbi"),qt=function(e){function t(){var t=e.call(this)||this;return t.className="NavigationBarDataItem",t.applyTheme(),t}return r.c(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),t}(zt.a),Ut=function(e){function t(){var t=e.call(this)||this;t.className="NavigationBar";var i=new N.a,n=new Et.a;n.valign="middle",n.paddingTop=8,n.paddingBottom=8,t.paddingBottom=2,t.links=new S.e(n),t._disposers.push(new S.c(t.links)),t._disposers.push(n),t._linksIterator=new k.ListIterator(t.links,function(){return t.links.create()}),t._linksIterator.createNewItems=!0;var r=new Gt.a;r.direction="right",r.width=8,r.height=12,r.fill=i.getFor("alternativeBackground"),r.fillOpacity=.5,r.valign="middle",r.marginLeft=10,r.marginRight=10,t.separators=new S.e(r),t._disposers.push(new S.c(t.separators)),t._disposers.push(r);var a=new Et.a;return t.activeLink=a,a.copyFrom(n),a.valign="middle",a.fontWeight="bold",t.width=Object(o.c)(100),t.layout="grid",t.dataFields.name="name",t.applyTheme(),t}return r.c(t,e),t.prototype.validateDataElements=function(){this.removeChildren(),this._linksIterator.reset(),e.prototype.validateDataElements.call(this)},t.prototype.validateDataElement=function(t){var i;if(e.prototype.validateDataElement.call(this,t),t.index<this.dataItems.length-1){(i=this._linksIterator.getLast()).parent=this;var n=this.separators.create();n.parent=this,n.valign="middle"}else(i=this.activeLink).events.copyFrom(this.links.template.events),i.hide(0),i.show(),i.parent=this;i.dataItem=t,i.text=t.name,i.validate()},t}(Bt.a);l.b.registeredClasses.NavigationBar=Ut,l.b.registeredClasses.NavigationBarDataItem=qt;var Kt=i("gqvf"),Zt=i("1Fjw"),Qt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarCursor",t.radius=Object(o.c)(100),t.innerRadius=Object(o.c)(0),t.applyTheme(),t.mask=void 0,t}return r.c(t,e),t.prototype.fitsToBounds=function(e){var t=u.getDistance(e);return t<this.truePixelRadius+1&&t>this.pixelInnerRadius-1},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),t.prototype.triggerMoveReal=function(t){this.xAxis&&(!this.xAxis||this.xAxis.cursorTooltipEnabled&&!this.xAxis.tooltip.disabled)||this.updateLineX(this.point),this.yAxis&&(!this.yAxis||this.yAxis.cursorTooltipEnabled&&!this.yAxis.tooltip.disabled)||this.updateLineY(this.point),this.updateSelection(),e.prototype.triggerMoveReal.call(this,t)},t.prototype.updateLineX=function(e){var t=this.pixelRadius,i=this.startAngle,n=this.endAngle,r=this.pixelInnerRadius;if(t>0&&m.isNumber(i)&&m.isNumber(n)&&m.isNumber(r)){var a=u.fitAngleToRange(u.getAngle(e),i,n),o=void 0;if(this.lineX&&this.lineX.visible){if(this.lineX.moveTo({x:0,y:0}),this.xAxis&&this.fullWidthLineX){var s=this.xAxis.currentItemStartPoint,l=this.xAxis.currentItemEndPoint;if(s&&l){var c=u.fitAngleToRange(u.getAngle(s),i,n),p=u.fitAngleToRange(u.getAngle(l),i,n)-c;i<n?p<0&&(p+=360):p>0&&(p-=360),a-=p/2,o=h.moveTo({x:r*u.cos(a),y:r*u.sin(a)})+h.lineTo({x:t*u.cos(a),y:t*u.sin(a)})+h.arcTo(a,p,t)+h.lineTo({x:r*u.cos(a+p),y:r*u.sin(a+p)})+h.arcTo(a+p,-p,r)}}o||(o=h.moveTo({x:r*u.cos(a),y:r*u.sin(a)})+h.lineTo({x:t*u.cos(a),y:t*u.sin(a)})),this.lineX.path=o}}},t.prototype.updateLineY=function(e){if(this.lineY&&this.lineY.visible){var t=this.startAngle,i=this.endAngle,n=this.truePixelRadius,r=u.fitToRange(u.getDistance(e),0,this.truePixelRadius);if(m.isNumber(r)&&m.isNumber(t)){this.lineY.moveTo({x:0,y:0});var a=void 0,o=i-t;if(this.yAxis&&this.fullWidthLineY){var s=this.yAxis.currentItemStartPoint,l=this.yAxis.currentItemEndPoint;if(s&&l){var c=u.fitToRange(u.getDistance(s),0,n);r=u.fitToRange(u.getDistance(l),0,n),a=h.moveTo({x:r*u.cos(t),y:r*u.sin(t)})+h.arcTo(t,o,r),a+=h.moveTo({x:c*u.cos(i),y:c*u.sin(i)})+h.arcTo(i,-o,c)}}a||(a=h.moveTo({x:r*u.cos(t),y:r*u.sin(t)})+h.arcTo(t,i-t,r)),this.lineY.path=a}}},t.prototype.updateSelection=function(){if(this._usesSelection){var e=this.downPoint;if(e){var t=this.point,i=this.pixelRadius,n=this.truePixelRadius,r=this.pixelInnerRadius,a=Math.min(this.startAngle,this.endAngle),o=Math.max(this.startAngle,this.endAngle),s=u.fitAngleToRange(u.getAngle(e),a,o),l=u.fitAngleToRange(u.getAngle(t),a,o),c=u.getDistance(e);if(c<n){var p=u.fitToRange(u.getDistance(t),0,n);this._prevAngle=l;var d=h.moveTo({x:0,y:0}),y=u.sin(s),g=u.cos(s),f=u.sin(l),m=u.cos(l),v=this.behavior;"zoomX"==v||"selectX"==v?d+=h.lineTo({x:i*g,y:i*y})+h.arcTo(s,l-s,i)+h.lineTo({x:r*m,y:r*f})+h.arcTo(l,s-l,r):"zoomY"==v||"selectY"==v?d=h.moveTo({x:p*u.cos(a),y:p*u.sin(a)})+h.arcTo(a,o-a,p)+h.lineTo({x:c*u.cos(o),y:c*u.sin(o)})+h.arcTo(o,a-o,c)+h.closePath():"zoomXY"==v&&(d=h.moveTo({x:p*u.cos(s),y:p*u.sin(s)})+h.arcTo(s,l-s,p)+h.lineTo({x:c*u.cos(l),y:c*u.sin(l)})+h.arcTo(l,s-l,c)+h.closePath()),this.selection.path=d}this.selection.moveTo({x:0,y:0})}}},t.prototype.getPositions=function(){if(this.chart){var e=this.pixelInnerRadius,t=this.truePixelRadius-e,i=this.startAngle,n=this.endAngle,r=(u.fitAngleToRange(u.getAngle(this.point),i,n)-i)/(n-i);this.xPosition=r,this.yPosition=u.fitToRange((u.getDistance(this.point)-e)/t,0,1)}},t.prototype.updatePoint=function(e){},t.prototype.handleXTooltipPosition=function(e){if(this.xAxis.cursorTooltipEnabled){var t=this.xAxis.tooltip;this.updateLineX(C.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.handleYTooltipPosition=function(e){if(this.yAxis.cursorTooltipEnabled){var t=this.yAxis.tooltip;this.updateLineY(C.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.updateLinePositions=function(e){},t.prototype.getRanges=function(){var e=this.downPoint;if(e){var t=this.upPoint;if(this.chart){var i=this.pixelRadius,n=this.startAngle,r=this.endAngle,a=u.fitAngleToRange(u.getAngle(e),this.startAngle,this.endAngle),o=u.fitAngleToRange(u.getAngle(t),this.startAngle,this.endAngle),s=u.fitToRange(u.getDistance(e),0,i),l=u.fitToRange(u.getDistance(t),0,i),h=0,c=1,p=0,d=1,y=this.behavior;if("zoomX"==y||"selectX"==y||"zoomXY"==y||"selectXY"==y){var g=r-n;h=u.round((a-n)/g,5),c=u.round((o-n)/g,5)}"zoomY"!=y&&"selectY"!=y&&"zoomXY"!=y&&"selectXY"!=y||(p=u.round(s/i,5),d=u.round(l/i,5)),this.xRange={start:Math.min(h,c),end:Math.max(h,c)},this.yRange={start:Math.min(p,d),end:Math.max(p,d)},"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide()}}},t.prototype.updateSize=function(){},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return C.relativeRadiusToValue(this.radius,this.truePixelRadius)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"truePixelRadius",{get:function(){return C.relativeToValue(Object(o.c)(100),u.min(this.innerWidth/2,this.innerHeight/2))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){var e=this.innerRadius;return e instanceof o.a&&(e=Object(o.c)(100*e.value*this.chart.innerRadiusModifyer)),C.relativeRadiusToValue(e,this.truePixelRadius)||0},enumerable:!0,configurable:!0}),t.prototype.fixPoint=function(e){return e},t}(Kt.a);l.b.registeredClasses.RadarCursor=Qt,window.am4charts=n}},["XFs4"]);