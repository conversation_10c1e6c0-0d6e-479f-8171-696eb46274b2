<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Is_verified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if(auth()->guard("web")->user()->email_verified !== 1){
            // return redirect()->route("register")->with("status","Your registration was successful, A verification link has been sent your email");
        }
        return $next($request);
    }
}
