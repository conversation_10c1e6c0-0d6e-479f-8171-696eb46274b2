<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Deposit extends Model
{
    use HasFactory;

    public $fillable = [
        'user_id',
        'trx_id',
        'name',
        'amount',
        'status',
        'payment_id',
        'pay_address',
        'pay_currency',
        'pay_amount',
        'network',
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }
}
