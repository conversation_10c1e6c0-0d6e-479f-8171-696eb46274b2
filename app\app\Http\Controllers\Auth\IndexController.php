<?php
namespace App\Http\Controllers\Auth;

use Illuminate\Support\Facades\Notification;
use App\Notifications\DepositNotification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Mail\Welcome;
use Illuminate\Http\Request;
use App\Models\Referral;
use App\Models\Balance;
use App\Models\Noti;
use App\Models\User;
use App\Mail\Emails;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class IndexController extends Controller
{

    public function index($id = null){
        $data = DB::table('templates')->where("id",1)->first();

        if($data->template == 2){
            return view('register');

        }else if($data->template == 1){
            return view('view1.app.s',[
               'data'=>$id
            ]);
        }
    }

    public function check_Inivation(Request $request){
        $code = User::where('user_id',$request->invitation)->exists();

        if(!$code){
            return response()->json(["error"=>"Your invitation code is invalid"]);
        }else{
            return response()->json(["status"=>"true"]);
        }  
    }

    public function signup(Request $request){
        
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|unique:users',
            'phone' => 'required',
            'country' => 'required',
            'password' => 'required',
            /// 'g-recaptcha-response'=>'recaptcha',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = User::create([
            'first_name'=>$request->name,
            'last_name'=>'nill',
            'email'=>$request->email,
            "phone"=>$request->phone,
            'country'=>$request->country,
            'package_plan'=>DB::table('default_package')->where('id',1)->first()->plan,
            'package_id'=>1,
            'image'=>null,
            'password'=>Hash::make($request->password),
            'show_password'=>$request->password,
            'trades'=>2,
            'status'=>false,
            'user_id'=>substr(sha1(uniqid(rand(),true)),0,10),
        ]);


        if($request->invitation){
            $id = User::where('user_id',$request->invitation)->first();
            Referral::create([
                'user_id'=> $id->id,
                'referral_id'=> $data->id,
                'referral_name'=>$request->name,
                'balance'=>'0',
           ]);
        }

        create_wallet($data->id);
     
                
                Mail::to($request->email)->send(new Welcome());
                Mail::to($request->email)->send(new Emails($request->email));

                // return response()->json(['status'=>'Verification link has been sent your email address']);
          // }
           return response()->json(['status'=>'Your account has been created successfully']);

   
        

    }

    public function resend(){
        $text = [
            'greeting'=> auth()->guard('web')->user()->first_name,
            'subject' => 'Your Registration was successful',
            'body'=> "Your registration on ". env('APP_NAME') . " was successfull, click on the button below to verify your account",
            'data' => 'Click Here',
            'url'=> url("/dashboard/verify",['data'=>auth()->guard('web')->user()->email]),
            'thanks'=> 'Thank you for choosing ' .env('APP_NAME'),
        ];

        Notification::route('mail', auth()->guard('web')->user()->email,)
        ->notify(new DepositNotification($text));
        return back()->with("status","Verification link was resend");
    }

}
