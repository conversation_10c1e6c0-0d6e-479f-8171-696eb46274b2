<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Staking;
use Illuminate\Support\Facades\DB;

class StakingController extends Controller
{
    public function index(){

        $datas = DB::table("user_payment")->get();

        $data = Staking::orderBy('id','desc')->get();
        return view('admin.exchange.staking',[
            'market'=>$data,
            'data'=>$datas
        ]);
    }

    public function add(Request $request){
        $data =  DB::table("user_payment")->where('id',$request->pair_id)->first();

        $check = Staking::where('pair',$data->name)->exists();

        if(!$check){
            Staking::create([
                'pair'=>$data->name,
                'symbol'=>$data->status,
                'percentage'=>$request->percentage,
                'volume'=>$request->volume,
            ]);
            return back()->with('status','staking asset created');
        }else{
            return back()->with('status','staking asset already created');

        }
    }

    public function delete($id){
        Staking::find($id)->delete();
        
        return back()->with('status','staking market deleted');
    }
}
