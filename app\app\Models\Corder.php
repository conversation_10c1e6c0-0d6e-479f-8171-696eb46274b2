<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Corder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'trade_id',
        'trades_id',
        'trader_name',
        'country',
        'amount',
        'commission',
        'win',
        'profit',
        'status',
        'types',
        'approved'
    ];
    
    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }
}
